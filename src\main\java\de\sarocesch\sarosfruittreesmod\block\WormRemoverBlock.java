package de.sarocesch.sarosfruittreesmod.block;

import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.level.storage.loot.LootContext;
import net.minecraft.world.level.block.state.properties.IntegerProperty;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.InteractionHand;
import net.minecraft.util.RandomSource;
import net.minecraft.sounds.SoundSource;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.network.chat.Component;
import net.minecraft.core.BlockPos;
import net.minecraft.advancements.AdvancementProgress;
import net.minecraft.advancements.AdvancementHolder;

import net.minecraftforge.registries.ForgeRegistries;
import net.minecraft.world.MenuProvider;

import de.sarocesch.sarosfruittreesmod.world.inventory.WormGUIMenu;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModItems;

import io.netty.buffer.Unpooled;

import java.util.List;
import java.util.Collections;

public class WormRemoverBlock extends Block {
    // Define block states
    public static final IntegerProperty STAGE = IntegerProperty.create("stage", 0, 30);

    // Animation stages mapping
    // 0 = empty, 1-3 = dirt filling, 4 = wheat seed, 5 = rotten apple, 6-29 = animation, 30 = final (GUI)

    public WormRemoverBlock(BlockBehaviour.Properties properties) {
        super(properties);
        this.registerDefaultState(this.stateDefinition.any().setValue(STAGE, 0));
    }

    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        builder.add(STAGE);
    }

    public boolean propagatesSkylightDown(BlockState state, BlockGetter reader, BlockPos pos) {
        return true;
    }

    public int getLightBlock(BlockState state, BlockGetter worldIn, BlockPos pos) {
        return 0;
    }

    @Override
    public VoxelShape getVisualShape(BlockState state, BlockGetter world, BlockPos pos, CollisionContext context) {
        return Shapes.empty();
    }

    @Override
    public VoxelShape getShape(BlockState state, BlockGetter world, BlockPos pos, CollisionContext context) {
        int stage = state.getValue(STAGE);
        if (stage == 30) { // Final stage (worm_remover_l_9)
            return box(0, 0, 0, 16, 24, 16);
        }
        return Shapes.block();
    }

    // Removed @Override as this method signature changed in 1.20.4
    public ItemStack getCloneItemStack(BlockState state, HitResult target, BlockGetter world, BlockPos pos, Player player) {
        return new ItemStack(this);
    }

    // In Forge 1.20.4 hat sich die Methode geändert
    public List<ItemStack> getDrops(BlockState state, net.minecraft.world.level.storage.loot.LootParams.Builder builder) {
        List<ItemStack> dropsOriginal = super.getDrops(state, builder);
        if (!dropsOriginal.isEmpty())
            return dropsOriginal;
        return Collections.singletonList(new ItemStack(this, 1));
    }

    @Override
    public void onPlace(BlockState blockstate, Level world, BlockPos pos, BlockState oldState, boolean moving) {
        super.onPlace(blockstate, world, pos, oldState, moving);
        int stage = blockstate.getValue(STAGE);
        if (stage >= 5 && stage < 30) { // Animation stages
            world.scheduleTick(pos, this, 10); // Schedule tick for animation
        }
    }

    @Override
    public void tick(BlockState blockstate, ServerLevel world, BlockPos pos, RandomSource random) {
        super.tick(blockstate, world, pos, random);
        int stage = blockstate.getValue(STAGE);

        if (stage >= 8 && stage < 30) {
            // Advance to the next stage
            int nextStage = stage + 1;
            if (nextStage == 30) {
                // At stage 29, there's a 20% chance to go to stage 30
                if (random.nextFloat() < 0.20f) {
                    world.setBlock(pos, blockstate.setValue(STAGE, 30), 3);
                    // Play celebration sounds
                    for (int i = 0; i < 8; i++) {
                        world.playSound(null, pos, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.withDefaultNamespace("entity.villager.yes")),
                            SoundSource.BLOCKS, 1, 1);
                    }
                } else {
                    // Loop back to stage 8
                    world.setBlock(pos, blockstate.setValue(STAGE, 8), 3);
                }
            } else {
                // Normal progression
                world.setBlock(pos, blockstate.setValue(STAGE, nextStage), 3);
            }

            // Schedule the next tick with minimal delay (half of previous)
            world.scheduleTick(pos, this, 1);
        }
    }

    public InteractionResult use(BlockState blockstate, Level world, BlockPos pos, Player entity, InteractionHand hand, BlockHitResult hit) {
        int x = pos.getX();
        int y = pos.getY();
        int z = pos.getZ();
        int stage = blockstate.getValue(STAGE);

        // Get held item
        ItemStack heldItem = ItemStack.EMPTY;
        if (entity != null) {
            heldItem = entity.getMainHandItem();
        }

        // Handle different stages
        if (stage == 0) {
            // Base block - check for dirt
            if (heldItem.getItem() == net.minecraft.world.level.block.Blocks.DIRT.asItem()) {
                world.setBlock(pos, blockstate.setValue(STAGE, 1), 3);

                // Play sound
                if (!world.isClientSide()) {
                    world.playSound(null, pos, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.withDefaultNamespace("block.composter.fill")),
                            SoundSource.BLOCKS, 1, 1);
                } else {
                    world.playLocalSound(x, y, z, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.withDefaultNamespace("block.composter.fill")),
                            SoundSource.BLOCKS, 1, 1, false);
                }

                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            } else {
                if (entity != null && !world.isClientSide()) {
                    entity.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.fill_with_dirt"), false);
                }
                return InteractionResult.SUCCESS;
            }
        } else if (stage == 1) {
            // First dirt stage - check for more dirt
            if (heldItem.getItem() == net.minecraft.world.level.block.Blocks.DIRT.asItem()) {
                world.setBlock(pos, blockstate.setValue(STAGE, 2), 3);

                // Play sound
                if (!world.isClientSide()) {
                    world.playSound(null, pos, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.withDefaultNamespace("block.composter.fill")),
                            SoundSource.BLOCKS, 1, 1);
                }

                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            }
        } else if (stage == 2) {
            // Second dirt stage - check for more dirt
            if (heldItem.getItem() == net.minecraft.world.level.block.Blocks.DIRT.asItem()) {
                world.setBlock(pos, blockstate.setValue(STAGE, 3), 3);

                // Play sound
                if (!world.isClientSide()) {
                    world.playSound(null, pos, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.withDefaultNamespace("block.composter.fill")),
                            SoundSource.BLOCKS, 1, 1);
                }

                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            }
        } else if (stage == 3) {
            // Third dirt stage - check for more dirt
            if (heldItem.getItem() == net.minecraft.world.level.block.Blocks.DIRT.asItem()) {
                world.setBlock(pos, blockstate.setValue(STAGE, 4), 3);

                // Play sound
                if (!world.isClientSide()) {
                    world.playSound(null, pos, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.withDefaultNamespace("block.composter.fill")),
                            SoundSource.BLOCKS, 1, 1);
                }

                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            }
        } else if (stage == 4) {
            // Wheat seed stage
            if (heldItem.getItem() == Items.WHEAT_SEEDS) {
                world.setBlock(pos, blockstate.setValue(STAGE, 5), 3);
                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            } else {
                if (entity != null && !world.isClientSide()) {
                    entity.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.need_wheat_seed"), false);
                }
                return InteractionResult.SUCCESS;
            }
        } else if (stage == 5) {
            // Rotten apple stage
            if (heldItem.getItem() == SarosFruitTreesModModItems.ROTTEN_APPLE.get()) {
                world.setBlock(pos, blockstate.setValue(STAGE, 7), 3);
                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            } else {
                if (entity != null && !world.isClientSide()) {
                    entity.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.need_rotten_apple"), false);
                }
                return InteractionResult.SUCCESS;
            }

        } else if (stage == 6) {
            // Glass block stage
            if (heldItem.getItem() == net.minecraft.world.level.block.Blocks.GLASS.asItem()) {
                world.setBlock(pos, blockstate.setValue(STAGE, 7), 3);
                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            } else {
                if (entity != null && !world.isClientSide()) {
                    entity.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.need_glass_block"), false);
                }
                return InteractionResult.SUCCESS;
            }
        } else if (stage == 7) {
            // Glass block stage
            if (heldItem.getItem() == net.minecraft.world.level.block.Blocks.GLASS.asItem()) {
                // Start animation
                world.setBlock(pos, blockstate.setValue(STAGE, 8), 3);
                // Start animation with minimal delay
                world.scheduleTick(pos, this, 1);
                heldItem.shrink(1);
                return InteractionResult.SUCCESS;
            } else {
                if (entity != null && !world.isClientSide()) {
                    entity.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.need_glass_block"), false);
                }
                return InteractionResult.SUCCESS;
            }
        } else if (stage >= 8 && stage < 30) {
            // Animation stages - show waiting message
            if (entity != null && !world.isClientSide()) {
                entity.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.wait_for_worm"), false);
            }
            return InteractionResult.SUCCESS;
        } else if (stage == 30) {
            // Final stage - open GUI
            if (entity instanceof ServerPlayer) {
                ServerPlayer serverPlayer = (ServerPlayer) entity;
                // Korrekte ResourceLocation-Erstellung
                ResourceLocation advancementId = ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", "wormi_time");

                // Holen des AdvancementHolders
                AdvancementHolder advancementHolder = serverPlayer.getServer().getAdvancements().get(advancementId);

                if (advancementHolder != null) {
                    // Arbeite mit dem AdvancementHolder
                    AdvancementProgress progress = serverPlayer.getAdvancements().getOrStartProgress(advancementHolder);

                    if (!progress.isDone()) {
                        for (String criteria : progress.getRemainingCriteria())
                            serverPlayer.getAdvancements().award(advancementHolder, criteria);
                    }
                }

                // Open GUI
                // In 1.20.4, NetworkHooks wurde entfernt. Wir verwenden direkt die Minecraft-API
                serverPlayer.openMenu(new MenuProvider() {
                    @Override
                    public Component getDisplayName() {
                        return Component.translatable("block.saros_fruit_trees_mod.worm_remover.gui_title");
                    }

                    @Override
                    public net.minecraft.world.inventory.AbstractContainerMenu createMenu(int id, net.minecraft.world.entity.player.Inventory inventory, Player player) {
                        return new WormGUIMenu(id, inventory, new net.minecraft.network.FriendlyByteBuf(Unpooled.buffer()).writeBlockPos(pos));
                    }
                }, pos);
            }
            return InteractionResult.SUCCESS;
        }

        return InteractionResult.SUCCESS;
    }

    // Method to handle the "Catch The Worm" button click
    public static void catchWorm(Level world, BlockPos pos, Entity entity) {
        if (entity == null || world == null)
            return;

        BlockState state = world.getBlockState(pos);
        if (state.getBlock() instanceof WormRemoverBlock) {
            // Reset to empty state
            world.setBlock(pos, state.setValue(STAGE, 0), 3);

            // Give rewards or other effects here if needed
            if (entity instanceof Player) {
                Player player = (Player) entity;
                player.displayClientMessage(Component.literal("You caught the worm!"), false);
            }
        }
    }
}

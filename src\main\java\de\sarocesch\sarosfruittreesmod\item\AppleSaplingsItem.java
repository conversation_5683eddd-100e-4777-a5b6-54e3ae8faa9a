
package de.sarocesch.sarosfruittreesmod.item;

import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.Item;
import net.minecraft.world.InteractionResult;

import de.sarocesch.sarosfruittreesmod.procedures.AppleSaplingPlacementProcedure;

public class AppleSaplingsItem extends Item {
	public AppleSaplingsItem(Properties properties) {
		super(properties.stacksTo(64).rarity(Rarity.COMMON));
	}

	@Override
	public InteractionResult useOn(UseOnContext context) {
		super.useOn(context);
		AppleSaplingPlacementProcedure.execute(context.getLevel(), context.getClickedPos().getX(), context.getClickedPos().getY(), context.getClickedPos().getZ(), context.getPlayer());
		return InteractionResult.SUCCESS;
	}
}


package de.sarocesch.sarosfruittreesmod.world.features;

import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.levelgen.structure.templatesystem.BlockIgnoreProcessor;
import net.minecraft.world.level.levelgen.feature.configurations.NoneFeatureConfiguration;
import net.minecraft.world.level.levelgen.feature.FeaturePlaceContext;
import net.minecraft.world.level.levelgen.feature.Feature;
import net.minecraft.world.level.levelgen.Heightmap;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.Level;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.resources.ResourceKey;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.util.WorldGenTreeHelper;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;

import java.util.Set;
import java.util.List;

public class Apple5Feature extends Feature<NoneFeatureConfiguration> {
	private final Set<ResourceKey<Level>> generate_dimensions = Set.of(Level.OVERWORLD);
	private final List<Block> base_blocks;
	private StructureTemplate template = null;

	public Apple5Feature() {
		super(NoneFeatureConfiguration.CODEC);
		base_blocks = List.of(Blocks.GRASS_BLOCK);
	}

	@Override
	public boolean place(FeaturePlaceContext<NoneFeatureConfiguration> context) {
		if (!generate_dimensions.contains(context.level().getLevel().dimension()))
			return false;

		// Get a random tree structure name
		String structureName = WorldGenTreeHelper.getRandomTreeStructureName(context.random());

		if (template == null)
			template = context.level().getLevel().getStructureManager().getOrCreate(ResourceLocation.parse("saros_fruit_trees_mod:" + structureName));
		if (template == null)
			return false;

		boolean anyPlaced = false;
		// Reduced spawn rate by 60% from 1.5% to 0.6% (6000 out of 1000000)
		if ((context.random().nextInt(1000000) + 1) <= 6000) {
			// Use the new group size method to determine how many trees to spawn
			int count = WorldGenTreeHelper.getTreeGroupSize(context.random());

			// Get the base position for this group of trees
			int baseX = context.origin().getX() + context.random().nextInt(16);
			int baseZ = context.origin().getZ() + context.random().nextInt(16);

			for (int a = 0; a < count; a++) {
				// Get vein offset for this tree in the group
				int[] offset = WorldGenTreeHelper.getVeinOffset(context.random(), a, count);

				// Apply offset to base position
				int i = baseX + offset[0];
				int k = baseZ + offset[1];
				int j = context.level().getHeight(Heightmap.Types.WORLD_SURFACE_WG, i, k) - 1;
				if (!base_blocks.contains(context.level().getBlockState(new BlockPos(i, j, k)).getBlock()))
					continue;

				BlockPos spawnTo = new BlockPos(i + 0, j + 0, k + 0);
				if (template.placeInWorld(context.level(), spawnTo, spawnTo,
						new StructurePlaceSettings()
							.setMirror(Mirror.values()[context.random().nextInt(2)])
							.setRotation(Rotation.values()[context.random().nextInt(3)])
							.setRandom(context.random())
							.addProcessor(BlockIgnoreProcessor.STRUCTURE_AND_AIR)
							.setIgnoreEntities(false),
						context.random(), 2)) {

					// Get a random fruit type
					FruitLeaveBlock.FruitType fruitType = WorldGenTreeHelper.getRandomFruitType(context.random());

					// Update all leave blocks to have the correct fruit type and randomized growth stages
					WorldGenTreeHelper.updateFruitLeaveBlocks(context.level(), i, j, k, 4, 8, fruitType);

					// Update leaf connections to prevent decay
					WorldGenTreeHelper.updateLeafConnections(context.level(), i, j, k, 4, 8);

					anyPlaced = true;
				}
			}
		}
		return anyPlaced;
	}
}

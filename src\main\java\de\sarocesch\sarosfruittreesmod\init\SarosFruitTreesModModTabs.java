package de.sarocesch.sarosfruittreesmod.init;

import net.minecraft.core.registries.Registries;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.eventbus.api.bus.BusGroup;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

public class SarosFruitTreesModModTabs {
    public static final DeferredRegister<CreativeModeTab> CREATIVE_MODE_TABS = DeferredRegister.create(Registries.CREATIVE_MODE_TAB, SarosFruitTreesModMod.MODID);

    public static final RegistryObject<CreativeModeTab> SAROS_FRUIT_MOD_TAB = CREATIVE_MODE_TABS.register("saros_fruit_mod",
            () -> CreativeModeTab.builder()
                    .title(Component.translatable("item_group.saros_fruit_trees_mod.saros_fruit_mod"))
                    .icon(() -> new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get()))
                    .displayItems((parameters, output) -> {
                        output.accept(SarosFruitTreesModModItems.APPLE_SAPLINGS.get());
                        output.accept(SarosFruitTreesModModItems.ROTTEN_APPLE.get());
                        output.accept(SarosFruitTreesModModItems.WORM.get());
                        output.accept(SarosFruitTreesModModBlocks.WORM_REMOVER.get().asItem());
                        output.accept(SarosFruitTreesModModBlocks.GOLDEN_FARMLAND.get().asItem());
                        output.accept(SarosFruitTreesModModItems.ORANGES_SAPLINGS.get());
                        output.accept(SarosFruitTreesModModItems.ORANGE.get());
                        output.accept(SarosFruitTreesModModItems.PEAR.get());
                        output.accept(SarosFruitTreesModModItems.PEAR_SAPLINGS.get());
                        output.accept(SarosFruitTreesModModItems.LEAF_REMOVER.get());
                    })
                    .build());

    public static void register(BusGroup eventBus) {
        CREATIVE_MODE_TABS.register(eventBus);
    }
}

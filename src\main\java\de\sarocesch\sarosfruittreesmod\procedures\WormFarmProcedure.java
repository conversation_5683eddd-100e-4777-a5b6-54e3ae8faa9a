package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.core.BlockPos;
import net.minecraft.advancements.AdvancementProgress;
import net.minecraft.advancements.AdvancementHolder;

import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;

public class WormFarmProcedure {
	public static void execute(LevelAccessor world, double x, double y, double z, Entity entity) {
		if (entity == null)
			return;
		if ((world.getBlockState(BlockPos.containing(x, y, z))).getBlock() == Blocks.COMPOSTER) {
			if (world instanceof ServerLevel _level) {
				ItemEntity entityToSpawn = new ItemEntity(_level, x, y, z, new ItemStack(SarosFruitTreesModModBlocks.GOLDEN_FARMLAND.get()));
				entityToSpawn.setPickUpDelay(15);
				entityToSpawn.setUnlimitedLifetime();
				_level.addFreshEntity(entityToSpawn);
			}
			if (entity instanceof ServerPlayer _player) {
				// Korrekte ResourceLocation-Erstellung
				ResourceLocation advancementId = ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", "finallytheworkhapaidoff");

				// Holen des AdvancementHolders
				AdvancementHolder advancementHolder = _player.getServer().getAdvancements().get(advancementId);

				if (advancementHolder != null) {
					// Arbeite mit dem AdvancementHolder
					AdvancementProgress _ap = _player.getAdvancements().getOrStartProgress(advancementHolder);

					if (!_ap.isDone()) {
						for (String criteria : _ap.getRemainingCriteria())
							_player.getAdvancements().award(advancementHolder, criteria);
					}
				}
			}
			(entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
		}
	}
}

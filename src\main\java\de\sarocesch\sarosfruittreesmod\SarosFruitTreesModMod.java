package de.sarocesch.sarosfruittreesmod;

import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModFeatures;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModItems;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModMenus;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModTabs;
import de.sarocesch.sarosfruittreesmod.network.ModNetwork;

import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.bus.BusGroup;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.invoke.MethodHandles;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

@Mod(SarosFruitTreesModMod.MODID)
public class SarosFruitTreesModMod {
	public static final Logger LOGGER = LogManager.getLogger(SarosFruitTreesModMod.class);
	public static final String MODID = "saros_fruit_trees_mod";

	protected final FMLJavaModLoadingContext context;

	private static final Collection<AbstractMap.SimpleEntry<Runnable, Integer>> workQueue = new ConcurrentLinkedQueue<>();

	public SarosFruitTreesModMod(FMLJavaModLoadingContext context) {
		this.context = context;

		// Optional: Config registrieren, falls vorhanden
		// ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, Config.SPEC);

		var modBusGroup = context.getModBusGroup();

		SarosFruitTreesModModBlocks.register(modBusGroup);
		SarosFruitTreesModModItems.register(modBusGroup);
		SarosFruitTreesModModMenus.REGISTRY.register(modBusGroup);
		SarosFruitTreesModModFeatures.REGISTRY.register(modBusGroup);
		SarosFruitTreesModModTabs.register(modBusGroup);

		FMLCommonSetupEvent.getBus(modBusGroup).addListener(this::commonSetup);

		ServerStartedEvent.BUS.addListener(this::onServerStarted);

		// Beispiel für Listener-Registrierung
		// BusGroup.DEFAULT.register(MethodHandles.lookup(), new SomeListenerClass());

		// Falls TickEvents weiterhin benötigt werden:
		// MinecraftForge.EVENT_BUS.register(this);
	}

	public SarosFruitTreesModMod() {
		this(FMLJavaModLoadingContext.get());
	}

	private void commonSetup(final FMLCommonSetupEvent event) {
		event.enqueueWork(ModNetwork::register);
	}

	private void onServerStarted(final ServerStartedEvent event) {
		//LOGGER.info("Saros Fruit Trees Mod erfolgreich gestartet.");
	}

	public static void queueServerWork(int tick, Runnable action) {
		workQueue.add(new AbstractMap.SimpleEntry<>(action, tick));
	}

	// Falls du weiterhin TickEvents im MinecraftForge.EVENT_BUS brauchst:
	@SubscribeEvent
	public void onServerTick(TickEvent.ServerTickEvent event) {
		if (event.type == TickEvent.Type.SERVER) {
			List<AbstractMap.SimpleEntry<Runnable, Integer>> actions = new ArrayList<>();
			workQueue.forEach(work -> {
				work.setValue(work.getValue() - 1);
				if (work.getValue() <= 0) {
					actions.add(work);
				}
			});
			actions.forEach(e -> e.getKey().run());
			workQueue.removeAll(actions);
		}
	}
}

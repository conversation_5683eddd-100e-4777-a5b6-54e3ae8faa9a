package de.sarocesch.sarosfruittreesmod.init;

import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;
import net.minecraft.client.renderer.ItemBlockRenderTypes;
import net.minecraft.client.renderer.RenderType;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;

@Mod.EventBusSubscriber(modid = SarosFruitTreesModMod.MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class SarosFruitTreesModModRenderTypes {

    @SubscribeEvent
    public static void registerRenderTypes(FMLClientSetupEvent event) {
        event.enqueueWork(() -> {
            //ItemBlockRenderTypes.setRenderLayer(SarosFruitTreesModModBlocks.FRUIT_SAPLING.get(), RenderType.cutout());
            //ItemBlockRenderTypes.setRenderLayer(SarosFruitTreesModModBlocks.FRUIT_LEAVE.get(), RenderType.cutoutMipped());
            //ItemBlockRenderTypes.setRenderLayer(SarosFruitTreesModModBlocks.WORM_REMOVER.get(), RenderType.translucent());
        });
    }
}

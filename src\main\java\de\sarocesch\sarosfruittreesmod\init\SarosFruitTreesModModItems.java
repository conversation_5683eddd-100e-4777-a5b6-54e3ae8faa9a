package de.sarocesch.sarosfruittreesmod.init;

import net.minecraftforge.eventbus.api.bus.BusGroup;
import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.DeferredRegister;

import net.minecraft.world.item.Item;
import net.minecraft.world.item.BlockItem;
import net.minecraft.core.registries.Registries;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;

import de.sarocesch.sarosfruittreesmod.item.WormItem;
import de.sarocesch.sarosfruittreesmod.item.RottenAppleItem;
import de.sarocesch.sarosfruittreesmod.item.PearSaplingsItem;
import de.sarocesch.sarosfruittreesmod.item.PearItem;
import de.sarocesch.sarosfruittreesmod.item.OrangesSaplingsItem;
import de.sarocesch.sarosfruittreesmod.item.OrangeItem;
import de.sarocesch.sarosfruittreesmod.item.LeafRemoverItem;
import de.sarocesch.sarosfruittreesmod.item.AppleSaplingsItem;
import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

public class SarosFruitTreesModModItems {
	public static final DeferredRegister<Item> REGISTRY = DeferredRegister.create(ForgeRegistries.ITEMS, SarosFruitTreesModMod.MODID);

	// Block items
	public static final RegistryObject<Item> FRUIT_SAPLING = REGISTRY.register("fruit_sapling",
		() -> new BlockItem(SarosFruitTreesModModBlocks.FRUIT_SAPLING.get(),
			new Item.Properties()
				.useItemDescriptionPrefix()
				.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":fruit_sapling")))));

	// Unified leave block with blockstates for all fruit types and growth stages
	public static final RegistryObject<Item> FRUIT_LEAVE = REGISTRY.register("fruit_leave",
		() -> new BlockItem(SarosFruitTreesModModBlocks.FRUIT_LEAVE.get(),
			new Item.Properties()
				.useItemDescriptionPrefix()
				.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":fruit_leave")))));

	public static final RegistryObject<Item> APPLE_SAPLINGS = REGISTRY.register("apple_saplings",
		() -> new AppleSaplingsItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":apple_saplings")))));

	public static final RegistryObject<Item> ROTTEN_APPLE = REGISTRY.register("rotten_apple",
		() -> new RottenAppleItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":rotten_apple")))));

	public static final RegistryObject<Item> WORM = REGISTRY.register("worm",
		() -> new WormItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":worm")))));

	public static final RegistryObject<Item> WORM_REMOVER = REGISTRY.register("worm_remover",
		() -> new BlockItem(SarosFruitTreesModModBlocks.WORM_REMOVER.get(),
			new Item.Properties()
				.useItemDescriptionPrefix()
				.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":worm_remover")))));

	public static final RegistryObject<Item> GOLDEN_FARMLAND = REGISTRY.register("golden_farmland",
		() -> new BlockItem(SarosFruitTreesModModBlocks.GOLDEN_FARMLAND.get(),
			new Item.Properties()
				.useItemDescriptionPrefix()
				.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":golden_farmland")))));

	// All leave blocks now use FRUIT_LEAVE with appropriate blockstates
	// ORANGES_SAPLING is now handled by FRUIT_SAPLING with fruit_type=orange
	public static final RegistryObject<Item> ORANGES_SAPLINGS = REGISTRY.register("oranges_saplings",
		() -> new OrangesSaplingsItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":oranges_saplings")))));

	public static final RegistryObject<Item> ORANGE = REGISTRY.register("orange",
		() -> new OrangeItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":orange")))));

	public static final RegistryObject<Item> PEAR = REGISTRY.register("pear",
		() -> new PearItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":pear")))));

	public static final RegistryObject<Item> PEAR_SAPLINGS = REGISTRY.register("pear_saplings",
		() -> new PearSaplingsItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":pear_saplings")))));

	public static final RegistryObject<Item> LEAF_REMOVER = REGISTRY.register("leaf_remover",
		() -> new LeafRemoverItem(new Item.Properties()
			.useItemDescriptionPrefix()
			.setId(ResourceKey.create(Registries.ITEM, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":leaf_remover")))));

	// Register method for ModLoader
	public static void register(BusGroup eventBus) {
		REGISTRY.register(eventBus);
	}
}

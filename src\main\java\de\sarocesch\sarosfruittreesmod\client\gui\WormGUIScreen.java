package de.sarocesch.sarosfruittreesmod.client.gui;

import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.network.chat.Component;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.components.Button;

import de.sarocesch.sarosfruittreesmod.world.inventory.WormGUIMenu;
import de.sarocesch.sarosfruittreesmod.network.WormGUIButtonMessage;
import de.sarocesch.sarosfruittreesmod.network.ModNetwork;

import java.util.HashMap;

import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.client.gui.GuiGraphics;

public class WormGUIScreen extends AbstractContainerScreen<WormGUIMenu> {
	private final static HashMap<String, Object> guistate = WormGUIMenu.guistate;
	private final Level world;
	private final int x, y, z;
	private final Player entity;
	But<PERSON> button_catch_the_worm;

	public WormGUIScreen(WormGUIMenu container, Inventory inventory, Component text) {
		super(container, inventory, text);
		this.world = container.world;
		this.x = container.x;
		this.y = container.y;
		this.z = container.z;
		this.entity = container.entity;
		this.imageWidth = 500;
		this.imageHeight = 500;
	}

	@Override
	public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {
		this.renderBackground(guiGraphics, mouseX, mouseY, partialTicks);
		super.render(guiGraphics, mouseX, mouseY, partialTicks);
		this.renderTooltip(guiGraphics, mouseX, mouseY);
	}

	@Override
	protected void renderBg(GuiGraphics guiGraphics, float partialTicks, int gx, int gy) {
		//RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);

	}

	@Override
	public boolean keyPressed(int key, int b, int c) {
		if (key == 256) {
			this.minecraft.player.closeContainer();
			return true;
		}
		return super.keyPressed(key, b, c);
	}

	@Override
	public void containerTick() {
		super.containerTick();
	}

	@Override
	protected void renderLabels(GuiGraphics guiGraphics, int mouseX, int mouseY) {
	}

	@Override
	public void onClose() {
		super.onClose();
	}

	@Override
	public void init() {
		super.init();
		button_catch_the_worm = Button.builder(Component.translatable("block.saros_fruit_trees_mod.worm_remover.button_catch_worm"), e -> {
			if (true) {
				// Sende die Nachricht über den neuen Netzwerk-Kanal
				ModNetwork.sendToServer(new WormGUIButtonMessage(0, x, y, z));
				// Lokale Verarbeitung für sofortige Reaktion
				WormGUIButtonMessage.handleButtonAction(entity, 0, x, y, z);
			}
		}).bounds(this.leftPos + 202, this.topPos + 240, 98, 20).build();
		guistate.put("button:button_catch_the_worm", button_catch_the_worm);
		this.addRenderableWidget(button_catch_the_worm);
	}
}

package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.core.BlockPos;

public class GoldenFarmlandBlockDestroyedByPlayerProcedure {
	public static void execute(LevelAccessor world, double x, double y, double z) {
		// Check and destroy Oak Logs up to 7 blocks above the farmland
		for (int i = 1; i <= 7; i++) {
			BlockPos pos = BlockPos.containing(x, y + i, z);
			BlockState blockState = world.getBlockState(pos);

			// Only destroy Oak Logs
			if (blockState.getBlock() == Blocks.OAK_LOG) {
				Block.dropResources(blockState, world, BlockPos.containing(x, y, z), null);
				world.destroyBlock(pos, false);
			}
		}
	}
}

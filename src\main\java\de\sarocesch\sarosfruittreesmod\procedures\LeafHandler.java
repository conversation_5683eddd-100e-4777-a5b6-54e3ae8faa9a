package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.Vec2;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.GameType;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.network.chat.Component;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.Direction;
import net.minecraft.core.BlockPos;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.CommandSource;
import net.minecraft.client.Minecraft;

import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;

/**
 * A unified handler for all leaf-related operations.
 * This class replaces the multiple individual leaf procedures.
 */
public class LeafHandler {

    /**
     * Enum to identify the type of leaf
     */
    public enum LeafType {
        APPLE,
        GOLDEN_APPLE,
        ORANGE,
        PEAR
    }

    /**
     * Enum to identify the stage of the leaf
     */
    public enum LeafStage {
        STAGE_1,
        STAGE_2,
        STAGE_3
    }

    /**
     * Check if a leaf block is connected to a log block within a certain distance
     * @param world The world
     * @param pos The position of the leaf block
     * @param maxDistance The maximum distance to check for logs
     * @return True if the leaf is connected to a log, false otherwise
     *
     * Note: This method is only used for legacy leaf blocks. The vanilla leaf decay
     * system is used for the new FruitLeaveBlock.
     */
    private static boolean isConnectedToLog(LevelAccessor world, BlockPos pos, int maxDistance) {
        // Check if the block at pos is a log
        if (world.getBlockState(pos).getBlock() instanceof net.minecraft.world.level.block.RotatedPillarBlock) {
            return true;
        }

        // If we've reached the maximum distance, stop searching
        if (maxDistance <= 0) {
            return false;
        }

        // Check all adjacent blocks
        for (Direction direction : Direction.values()) {
            BlockPos adjacentPos = pos.relative(direction);
            Block adjacentBlock = world.getBlockState(adjacentPos).getBlock();

            // If the adjacent block is a log, we found a connection
            if (adjacentBlock instanceof net.minecraft.world.level.block.RotatedPillarBlock) {
                return true;
            }

            // If the adjacent block is a leaf, recursively check if it's connected to a log
            if (adjacentBlock instanceof net.minecraft.world.level.block.LeavesBlock) {
                // Avoid checking the same block again by reducing the max distance
                if (isConnectedToLog(world, adjacentPos, maxDistance - 1)) {
                    return true;
                }
            }
        }

        // If we didn't find any connection, return false
        return false;
    }

    /**
     * Handle random tick updates for leaves
     * @param world The world
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @param leafType The type of leaf
     * @param currentStage The current stage of the leaf
     */
    public static void handleRandomTick(LevelAccessor world, double x, double y, double z, LeafType leafType, LeafStage currentStage) {
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState state = world.getBlockState(pos);
        Block block = state.getBlock();

        // Check if it's the new FruitLeaveBlock
        if (block instanceof FruitLeaveBlock) {
            // The FruitLeaveBlock handles its own random ticks
            return;
        }

        // Legacy handling for old blocks
        // Check if the leaf is connected to a log within 6 blocks (vanilla Minecraft uses 6)
        boolean isConnected = isConnectedToLog(world, pos, 6);

        // If the leaf is connected to a log, it can grow to the next stage
        if (isConnected) {
            // Random chance to progress to the next stage
            double progressChance = 0.1; // Default 10% chance

            // Adjust chance based on leaf type
            if (leafType == LeafType.APPLE) {
                progressChance = 0.2; // 20% chance for apple leaves
            } else if (leafType == LeafType.GOLDEN_APPLE) {
                progressChance = 0.2; // 20% chance for golden apple leaves
            } else if (leafType == LeafType.ORANGE) {
                progressChance = 0.1; // 10% chance for orange leaves
            }

            // Check if we should progress
            if (Math.random() < progressChance) {
                // Get the next stage block
                Block nextStageBlock = getNextStageBlock(leafType, currentStage);

                // If there is a next stage, set the block
                if (nextStageBlock != null) {
                    BlockState newState = nextStageBlock.defaultBlockState();
                    BlockState oldState = world.getBlockState(pos);

                    // We don't need to copy properties for this case
                    // Just set the block directly

                    // Set the block
                    world.setBlock(pos, newState, 3);
                }
            }
        }
        // If the leaf is not connected to a log, it should decay (vanilla Minecraft behavior)
        // For the new FruitLeaveBlock, this is handled by the vanilla leaf decay system
        // For legacy blocks, we don't need to implement decay here
    }

    /**
     * Handle right-click with bonemeal on leaves
     * @param world The world
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @param entity The entity that right-clicked
     * @param leafType The type of leaf
     * @param currentStage The current stage of the leaf
     */
    public static void handleBonemeal(LevelAccessor world, double x, double y, double z, Entity entity, LeafType leafType, LeafStage currentStage) {
        if (entity == null) {
            return;
        }

        // Check if the block is the new FruitLeaveBlock
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState state = world.getBlockState(pos);
        Block block = state.getBlock();

        if (block instanceof FruitLeaveBlock) {
            // Handle with blockstate system
            handleBonemealForFruitLeaveBlock(world, pos, state, entity);
            return;
        }

        // Legacy handling for old blocks
        // Check if the player is holding bonemeal
        if ((entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem() == Items.BONE_MEAL) {
            // 30% chance for bonemeal to work
            if (Math.random() < 0.3) {
                // Get the next stage block
                Block nextStageBlock = getNextStageBlock(leafType, currentStage);

                // If there is a next stage, set the block
                if (nextStageBlock != null) {
                    // Check if the player is in creative mode
                    boolean isCreative = new Object() {
                        public boolean checkGamemode(Entity _ent) {
                            if (_ent instanceof ServerPlayer _serverPlayer) {
                                return _serverPlayer.gameMode.getGameModeForPlayer() == GameType.CREATIVE;
                            } else if (_ent.level().isClientSide() && _ent instanceof Player _player) {
                                return Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()) != null
                                        && Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()).getGameMode() == GameType.CREATIVE;
                            }
                            return false;
                        }
                    }.checkGamemode(entity);

                    // Show particles and play sound
                    if (world instanceof ServerLevel _level) {
                        _level.sendParticles(ParticleTypes.HAPPY_VILLAGER, x, y, z, 10, 1, 1, 1, 1);
                    }

                    // Play bonemeal sound
                    if (world instanceof Level _level) {
                        if (!_level.isClientSide()) {
                            _level.playSound(null, new BlockPos((int)x, (int)y, (int)z), net.minecraft.sounds.SoundEvents.BONE_MEAL_USE, net.minecraft.sounds.SoundSource.BLOCKS, 1.0F, 1.0F);
                        }
                    }

                    // Consume bonemeal if not in creative mode
                    if (!isCreative) {
                        (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
                    }

                    // Set the block to the next stage
                    BlockState newState = nextStageBlock.defaultBlockState();

                    // Set the block
                    world.setBlock(pos, newState, 3);
                }
            } else {
                // Bonemeal failed, but still consume bonemeal and play sound
                // No smoke particles anymore

                // Play bonemeal sound
                if (world instanceof Level _level) {
                    if (!_level.isClientSide()) {
                        _level.playSound(null, new BlockPos((int)x, (int)y, (int)z), net.minecraft.sounds.SoundEvents.BONE_MEAL_USE, net.minecraft.sounds.SoundSource.BLOCKS, 1.0F, 1.0F);
                    }
                }

                // Consume bonemeal if not in creative mode
                if (!(new Object() {
                    public boolean checkGamemode(Entity _ent) {
                        if (_ent instanceof ServerPlayer _serverPlayer) {
                            return _serverPlayer.gameMode.getGameModeForPlayer() == GameType.CREATIVE;
                        } else if (_ent.level().isClientSide() && _ent instanceof Player _player) {
                            return Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()) != null
                                    && Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()).getGameMode() == GameType.CREATIVE;
                        }
                        return false;
                    }
                }.checkGamemode(entity))) {
                    (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
                }
            }
        }
    }

    /**
     * Handle bonemeal for the new FruitLeaveBlock
     */
    private static void handleBonemealForFruitLeaveBlock(LevelAccessor world, BlockPos pos, BlockState state, Entity entity) {
        // Check if the player is holding bonemeal
        if ((entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem() == Items.BONE_MEAL) {
            // Get current growth stage
            FruitLeaveBlock.GrowthStage currentStage = state.getValue(FruitLeaveBlock.GROWTH_STAGE);

            // Check if there's a next stage
            FruitLeaveBlock.GrowthStage nextStage = null;
            if (currentStage == FruitLeaveBlock.GrowthStage.STAGE_1) {
                nextStage = FruitLeaveBlock.GrowthStage.STAGE_2;
            } else if (currentStage == FruitLeaveBlock.GrowthStage.STAGE_2) {
                nextStage = FruitLeaveBlock.GrowthStage.STAGE_3;
            }

            // 30% chance for bonemeal to work
            boolean success = Math.random() < 0.3 && nextStage != null;

            // Check if the player is in creative mode
            boolean isCreative = new Object() {
                public boolean checkGamemode(Entity _ent) {
                    if (_ent instanceof ServerPlayer _serverPlayer) {
                        return _serverPlayer.gameMode.getGameModeForPlayer() == GameType.CREATIVE;
                    } else if (_ent.level().isClientSide() && _ent instanceof Player _player) {
                        return Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()) != null
                                && Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()).getGameMode() == GameType.CREATIVE;
                    }
                    return false;
                }
            }.checkGamemode(entity);

            // Play bonemeal sound
            if (world instanceof Level _level) {
                if (!_level.isClientSide()) {
                    _level.playSound(null, pos, SoundEvents.BONE_MEAL_USE, SoundSource.BLOCKS, 1.0F, 1.0F);
                }
            }

            // Consume bonemeal if not in creative mode
            if (!isCreative) {
                (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
            }

            if (success) {
                // Show particles
                if (world instanceof ServerLevel _level) {
                    _level.sendParticles(ParticleTypes.HAPPY_VILLAGER, pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5, 10, 1, 1, 1, 1);
                }

                // Update the block state
                world.setBlock(pos, state.setValue(FruitLeaveBlock.GROWTH_STAGE, nextStage), 3);
            }
        }
    }

    /**
     * Handle harvesting fruits from leaves
     * @param world The world
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @param entity The entity that right-clicked
     */
    public static void handleHarvest(LevelAccessor world, double x, double y, double z, Entity entity) {
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState state = world.getBlockState(pos);
        Block clickedBlock = state.getBlock();

        // Check if it's the new FruitLeaveBlock
        if (clickedBlock instanceof FruitLeaveBlock) {
            FruitLeaveBlock.FruitType fruitType = state.getValue(FruitLeaveBlock.FRUIT_TYPE);
            FruitLeaveBlock.GrowthStage growthStage = state.getValue(FruitLeaveBlock.GROWTH_STAGE);

            // Only harvest if at stage 2 or 3
            if (growthStage == FruitLeaveBlock.GrowthStage.STAGE_2 || growthStage == FruitLeaveBlock.GrowthStage.STAGE_3) {
                if (fruitType == FruitLeaveBlock.FruitType.APPLE) {
                    harvestAppleFromFruitLeave(world, pos, state);
                } else if (fruitType == FruitLeaveBlock.FruitType.GOLDEN_APPLE) {
                    harvestGoldenAppleFromFruitLeave(world, pos, state);
                } else if (fruitType == FruitLeaveBlock.FruitType.ORANGE) {
                    harvestOrangeFromFruitLeave(world, pos, state);
                } else if (fruitType == FruitLeaveBlock.FruitType.PEAR) {
                    harvestPearFromFruitLeave(world, pos, state);
                }
            }
            return;
        }

        // All leaves are now handled by FruitLeaveBlock with blockstates
        // If we get here, it means the block is not a FruitLeaveBlock
        // This should not happen in normal gameplay, but we'll handle it just in case

        // Create a FruitLeaveBlock state with appropriate type and stage
        BlockState newState = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get().defaultBlockState();

        // Try to determine the type and stage from the block's name
        String blockName = clickedBlock.getDescriptionId().toLowerCase();

        if (blockName.contains("apple")) {
            if (blockName.contains("g_apple") || blockName.contains("golden")) {
                // Golden apple leaf
                newState = newState.setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.GOLDEN_APPLE);
                if (blockName.contains("1")) {
                    newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1);
                } else {
                    newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_2);
                }
                harvestGoldenAppleFromFruitLeave(world, pos, newState);
            } else {
                // Regular apple leaf
                newState = newState.setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.APPLE);
                if (blockName.contains("1")) {
                    newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1);
                } else if (blockName.contains("2")) {
                    newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_2);
                } else {
                    newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_3);
                }
                harvestAppleFromFruitLeave(world, pos, newState);
            }
        } else if (blockName.contains("orange")) {
            // Orange leaf
            newState = newState.setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.ORANGE);
            if (blockName.contains("1")) {
                newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1);
            } else if (blockName.contains("2")) {
                newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_2);
            } else {
                newState = newState.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_3);
            }
            harvestOrangeFromFruitLeave(world, pos, newState);
        }
    }

    /**
     * Helper method to harvest an apple from the new FruitLeaveBlock
     */
    private static void harvestAppleFromFruitLeave(LevelAccessor world, BlockPos pos, BlockState state) {
        if (Math.random() < 0.9) {
            // Give apple to player
            if (world instanceof ServerLevel _level)
                _level.getServer().getCommands().performPrefixedCommand(
                    new CommandSourceStack(CommandSource.NULL, new Vec3(pos.getX(), pos.getY(), pos.getZ()), Vec2.ZERO, _level, 4, "",
                    Component.literal(""), _level.getServer(), null).withSuppressedOutput(), "give @p apple");

            // Reset to stage 1
            world.setBlock(pos, state.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1), 3);
        }

        // Small chance for rotten apple
        if (Math.random() < 0.01) {
            if (world instanceof ServerLevel _level)
                _level.getServer().getCommands().performPrefixedCommand(
                    new CommandSourceStack(CommandSource.NULL, new Vec3(pos.getX(), pos.getY(), pos.getZ()), Vec2.ZERO, _level, 4, "",
                    Component.literal(""), _level.getServer(), null).withSuppressedOutput(), "give @p saros_fruit_trees_mod:rotten_apple");

            // Reset to stage 1
            world.setBlock(pos, state.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1), 3);
        }
    }

    /**
     * Helper method to harvest a golden apple from the new FruitLeaveBlock
     */
    private static void harvestGoldenAppleFromFruitLeave(LevelAccessor world, BlockPos pos, BlockState state) {
        if (Math.random() < 0.9) {
            // Give golden apple to player
            if (world instanceof ServerLevel _level)
                _level.getServer().getCommands().performPrefixedCommand(
                    new CommandSourceStack(CommandSource.NULL, new Vec3(pos.getX(), pos.getY(), pos.getZ()), Vec2.ZERO, _level, 4, "",
                    Component.literal(""), _level.getServer(), null).withSuppressedOutput(), "give @p golden_apple");

            // Convert to normal apple leaf (STAGE_1)
            world.setBlock(pos, state
                .setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1)
                .setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.APPLE), 3);
        }

        // Small chance for rotten apple
        if (Math.random() < 0.01) {
            if (world instanceof ServerLevel _level)
                _level.getServer().getCommands().performPrefixedCommand(
                    new CommandSourceStack(CommandSource.NULL, new Vec3(pos.getX(), pos.getY(), pos.getZ()), Vec2.ZERO, _level, 4, "",
                    Component.literal(""), _level.getServer(), null).withSuppressedOutput(), "give @p saros_fruit_trees_mod:rotten_apple");

            // Convert to normal apple leaf (STAGE_1)
            world.setBlock(pos, state
                .setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1)
                .setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.APPLE), 3);
        }
    }

    /**
     * Helper method to harvest an orange from the new FruitLeaveBlock
     */
    private static void harvestOrangeFromFruitLeave(LevelAccessor world, BlockPos pos, BlockState state) {
        if (Math.random() < 0.9) {
            // Give orange to player
            if (world instanceof ServerLevel _level)
                _level.getServer().getCommands().performPrefixedCommand(
                    new CommandSourceStack(CommandSource.NULL, new Vec3(pos.getX(), pos.getY(), pos.getZ()), Vec2.ZERO, _level, 4, "",
                    Component.literal(""), _level.getServer(), null).withSuppressedOutput(), "give @p saros_fruit_trees_mod:orange");

            // Reset to stage 1
            world.setBlock(pos, state.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1), 3);
        }
    }

    /**
     * Helper method to harvest a pear from the new FruitLeaveBlock
     */
    private static void harvestPearFromFruitLeave(LevelAccessor world, BlockPos pos, BlockState state) {
        if (Math.random() < 0.9) {
            // Give pear to player
            if (world instanceof ServerLevel _level)
                _level.getServer().getCommands().performPrefixedCommand(
                    new CommandSourceStack(CommandSource.NULL, new Vec3(pos.getX(), pos.getY(), pos.getZ()), Vec2.ZERO, _level, 4, "",
                    Component.literal(""), _level.getServer(), null).withSuppressedOutput(), "give @p saros_fruit_trees_mod:pear");

            // Reset to stage 1
            world.setBlock(pos, state.setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_1), 3);
        }
    }

    /**
     * Helper method to harvest an apple - deprecated, use harvestAppleFromFruitLeave instead
     * @deprecated Use {@link #harvestAppleFromFruitLeave(LevelAccessor, BlockPos, BlockState)} instead
     */
    @Deprecated
    private static void harvestApple(LevelAccessor world, double x, double y, double z) {
        // Convert to BlockPos and create a FruitLeaveBlock state
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState state = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get().defaultBlockState()
            .setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.APPLE)
            .setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_3);

        // Use the new method
        harvestAppleFromFruitLeave(world, pos, state);
    }

    /**
     * Helper method to harvest a golden apple - deprecated, use harvestGoldenAppleFromFruitLeave instead
     * @deprecated Use {@link #harvestGoldenAppleFromFruitLeave(LevelAccessor, BlockPos, BlockState)} instead
     */
    @Deprecated
    private static void harvestGoldenApple(LevelAccessor world, double x, double y, double z) {
        // Convert to BlockPos and create a FruitLeaveBlock state
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState state = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get().defaultBlockState()
            .setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.GOLDEN_APPLE)
            .setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_2);

        // Use the new method
        harvestGoldenAppleFromFruitLeave(world, pos, state);
    }

    /**
     * Helper method to harvest an orange - deprecated, use harvestOrangeFromFruitLeave instead
     * @deprecated Use {@link #harvestOrangeFromFruitLeave(LevelAccessor, BlockPos, BlockState)} instead
     */
    @Deprecated
    private static void harvestOrange(LevelAccessor world, double x, double y, double z) {
        // Convert to BlockPos and create a FruitLeaveBlock state
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState state = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get().defaultBlockState()
            .setValue(FruitLeaveBlock.FRUIT_TYPE, FruitLeaveBlock.FruitType.ORANGE)
            .setValue(FruitLeaveBlock.GROWTH_STAGE, FruitLeaveBlock.GrowthStage.STAGE_2);

        // Use the new method
        harvestOrangeFromFruitLeave(world, pos, state);
    }

    /**
     * Get the next stage block for a leaf type and stage
     * @param leafType The type of leaf
     * @param currentStage The current stage of the leaf
     * @return The next stage block, or null if there is no next stage
     * @deprecated Use FruitLeaveBlock with appropriate blockstates instead
     */
    @Deprecated
    private static Block getNextStageBlock(LeafType leafType, LeafStage currentStage) {
        // Create a FruitLeaveBlock with appropriate blockstates
        Block fruitLeaveBlock = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get();

        // This method is deprecated and should not be used anymore
        // Return the FruitLeaveBlock as a fallback
        return fruitLeaveBlock;
    }
}

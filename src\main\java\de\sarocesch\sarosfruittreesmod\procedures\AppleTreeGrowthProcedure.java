package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.levelgen.structure.templatesystem.BlockIgnoreProcessor;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.LeavesBlock;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;
import de.sarocesch.sarosfruittreesmod.util.TreeStructureHelper;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;

/**
 * Procedure for growing apple trees from saplings.
 * This procedure is called when an apple sapling reaches its final growth stage
 * or when bonemeal is applied to a fully grown apple sapling.
 */
public class AppleTreeGrowthProcedure {
	public static void execute(LevelAccessor world, double x, double y, double z) {
		// First remove the sapling block
		world.setBlock(BlockPos.containing(x, y, z), Blocks.AIR.defaultBlockState(), 3);

		if (world instanceof ServerLevel _serverworld) {
			// Get a random tree structure name for apple trees
			String selectedName = TreeStructureHelper.getRandomTreeStructureName(_serverworld, false, false);
			StructureTemplate template = null;

			// Try the old format first (which seems to be what was used)
			template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.tryParse("saros_fruit_trees_mod:" + selectedName));

			// If that fails, try the new format
			if (template == null) {
				template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.tryParse("saros_fruit_trees_mod:" + selectedName));
			}

			// If that still fails, try each structure name as a fallback
			if (template == null) {
				String[] structureNames = {"baum1", "baum2", "baum3", "baum4", "baum5"};
				for (String name : structureNames) {
					if (!name.equals(selectedName)) { // Skip the one we already tried
						// Try the old format
						template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.tryParse("saros_fruit_trees_mod:" + name));

						// If that fails, try the new format
						if (template == null) {
							template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.tryParse("saros_fruit_trees_mod:" + name));
						}

						// If we found a template, break out of the loop
						if (template != null) {
							break;
						}
					}
				}
			}

			// If we have a template, place it
			if (template != null) {
				// Place the structure
				template.placeInWorld(_serverworld, BlockPos.containing(x - 2, y, z - 2), BlockPos.containing(x - 2, y, z - 2),
						new StructurePlaceSettings()
							.setRotation(Rotation.NONE)
							.setMirror(Mirror.NONE)
							.setIgnoreEntities(false)
							.addProcessor(BlockIgnoreProcessor.STRUCTURE_BLOCK), // Ignore structure blocks
						_serverworld.random, 3);

				// Fix waterlogged leaves in the area around the tree
				for (int dx = -4; dx <= 4; dx++) {
					for (int dy = 0; dy <= 8; dy++) {
						for (int dz = -4; dz <= 4; dz++) {
							BlockPos pos = BlockPos.containing(x + dx, y + dy, z + dz);
							BlockState state = world.getBlockState(pos);

							// Check if the block is any type of leaf block
							if (state.getBlock() instanceof LeavesBlock ||
								state.getBlock() == SarosFruitTreesModModBlocks.FRUIT_LEAVE.get()) {

								// Check if the block has the waterlogged property and is waterlogged
								if (state.hasProperty(BlockStateProperties.WATERLOGGED) &&
									state.getValue(BlockStateProperties.WATERLOGGED)) {

									// Set waterlogged to false
									world.setBlock(pos, state.setValue(BlockStateProperties.WATERLOGGED, false), 3);
								}
							}
						}
					}
				}

				// Update all leave blocks to have the correct fruit type and randomized growth stages
				TreeStructureHelper.updateFruitLeaveBlocks(world, x, y, z, 4, 8,
					FruitLeaveBlock.FruitType.APPLE);

				// Update leaf connections to prevent decay
				TreeStructureHelper.updateLeafConnections(world, x, y, z, 4, 8);
			}
		}
	}
}

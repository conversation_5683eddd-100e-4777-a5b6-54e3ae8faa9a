package de.sarocesch.sarosfruittreesmod.events;

import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.core.BlockPos;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;

import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;
import de.sarocesch.sarosfruittreesmod.block.FruitSaplingBlock;
import de.sarocesch.sarosfruittreesmod.block.WormRemoverBlock;

@Mod.EventBusSubscriber(modid = SarosFruitTreesModMod.MODID)
public class BlockInteractionHandler {

    @SubscribeEvent
    public static void onRightClickBlock(PlayerInteractEvent.RightClickBlock event) {
        Player player = event.getEntity();
        Level level = player.level();
        BlockPos pos = event.getPos();
        BlockState state = level.getBlockState(pos);
        InteractionHand hand = event.getHand();

        // Check if the block is one of our custom blocks
        if (state.getBlock() instanceof FruitLeaveBlock) {
            // Handle FruitLeaveBlock interaction
            FruitLeaveBlock block = (FruitLeaveBlock) state.getBlock();
            Vec3 hitVec = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
            BlockHitResult hit = new BlockHitResult(hitVec, event.getFace(), pos, false);
            // Call the use method but don't cancel the event
            // This allows the original interaction to proceed and trigger advancements
            InteractionResult result = block.use(state, level, pos, player, hand, hit);
            // We don't cancel the event to allow advancements to trigger

        } else if (state.getBlock() instanceof FruitSaplingBlock) {
            // Handle FruitSaplingBlock interaction
            FruitSaplingBlock block = (FruitSaplingBlock) state.getBlock();
            Vec3 hitVec = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
            BlockHitResult hit = new BlockHitResult(hitVec, event.getFace(), pos, false);
            ItemStack itemstack = player.getItemInHand(hand);

            // Check if player is holding bonemeal
            if (itemstack.getItem() == Items.BONE_MEAL) {
                // Show particles on both client and server
                if (level.isClientSide) {
                    // Client-side particle effects
                    for (int i = 0; i < 15; i++) {
                        double d0 = level.random.nextGaussian() * 0.02D;
                        double d1 = level.random.nextGaussian() * 0.02D;
                        double d2 = level.random.nextGaussian() * 0.02D;
                        double x = pos.getX() + 0.5 + (level.random.nextFloat() - 0.5) * 0.8;
                        double y = pos.getY() + 0.5 + (level.random.nextFloat() - 0.5) * 0.8;
                        double z = pos.getZ() + 0.5 + (level.random.nextFloat() - 0.5) * 0.8;
                        level.addParticle(ParticleTypes.HAPPY_VILLAGER, x, y, z, d0, d1, d2);
                    }
                } else {
                    // Server-side particle event
                    level.levelEvent(2005, pos, 0); // Standard bonemeal effect
                }
            }

            // Call the use method but don't cancel the event
            // This allows the original interaction to proceed and trigger advancements
            InteractionResult result = block.use(state, level, pos, player, hand, hit);
            // We don't cancel the event to allow advancements to trigger

        } else if (state.getBlock() instanceof WormRemoverBlock) {
            // Handle WormRemoverBlock interaction
            WormRemoverBlock block = (WormRemoverBlock) state.getBlock();
            Vec3 hitVec = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
            BlockHitResult hit = new BlockHitResult(hitVec, event.getFace(), pos, false);
            // Call the use method but don't cancel the event
            // This allows the original interaction to proceed and trigger advancements
            InteractionResult result = block.use(state, level, pos, player, hand, hit);
            // We don't cancel the event to allow advancements to trigger
        }
    }


}

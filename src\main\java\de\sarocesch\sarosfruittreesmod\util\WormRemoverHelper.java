package de.sarocesch.sarosfruittreesmod.util;

import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;
import de.sarocesch.sarosfruittreesmod.block.WormRemoverBlock;

/**
 * Helper class for transitioning from multiple worm remover blocks to a single block with states
 */
public class WormRemoverHelper {

    /**
     * Sets the worm remover block to the specified stage
     * 
     * @param world The world
     * @param pos The position
     * @param stage The stage to set (0-30)
     */
    public static void setWormRemoverStage(LevelAccessor world, BlockPos pos, int stage) {
        BlockState currentState = world.getBlockState(pos);
        
        // Check if it's already a WormRemoverBlock
        if (currentState.getBlock() instanceof WormRemoverBlock) {
            world.setBlock(pos, currentState.setValue(WormRemoverBlock.STAGE, stage), 3);
        } else {
            // Replace with a new WormRemoverBlock at the specified stage
            world.setBlock(pos, SarosFruitTreesModModBlocks.WORM_REMOVER.get().defaultBlockState()
                    .setValue(WormRemoverBlock.STAGE, stage), 3);
        }
    }
    
    /**
     * Gets the current stage of a worm remover block
     * 
     * @param world The world
     * @param pos The position
     * @return The current stage, or -1 if not a worm remover block
     */
    public static int getWormRemoverStage(LevelAccessor world, BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        if (state.getBlock() instanceof WormRemoverBlock) {
            return state.getValue(WormRemoverBlock.STAGE);
        }
        return -1;
    }
}

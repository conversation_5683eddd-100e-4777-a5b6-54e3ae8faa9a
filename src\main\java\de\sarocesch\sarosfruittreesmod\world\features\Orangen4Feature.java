
package de.sarocesch.sarosfruittreesmod.world.features;

import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.levelgen.structure.templatesystem.BlockIgnoreProcessor;
import de.sarocesch.sarosfruittreesmod.util.StructureHelper;
import net.minecraft.world.level.levelgen.feature.configurations.NoneFeatureConfiguration;
import net.minecraft.world.level.levelgen.feature.FeaturePlaceContext;
import net.minecraft.world.level.levelgen.feature.Feature;
import net.minecraft.world.level.levelgen.Heightmap;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.Level;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.resources.ResourceKey;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.util.WorldGenTreeHelper;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;

import java.util.Set;
import java.util.List;

public class Orangen4Feature extends Feature<NoneFeatureConfiguration> {
	private final Set<ResourceKey<Level>> generate_dimensions = Set.of(Level.OVERWORLD);
	private final List<Block> base_blocks;
	private StructureTemplate template = null;

	public Orangen4Feature() {
		super(NoneFeatureConfiguration.CODEC);
		base_blocks = List.of(Blocks.GRASS_BLOCK);
	}

	@Override
	public boolean place(FeaturePlaceContext<NoneFeatureConfiguration> context) {
		if (!generate_dimensions.contains(context.level().getLevel().dimension()))
			return false;
		if (template == null)
			template = context.level().getLevel().getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", "obaum4"));
		if (template == null)
			return false;
		boolean anyPlaced = false;
		if ((context.random().nextInt(1000000) + 1) <= 3000) {
			int count = context.random().nextInt(1) + 1;
			for (int a = 0; a < count; a++) {
				int i = context.origin().getX() + context.random().nextInt(16);
				int k = context.origin().getZ() + context.random().nextInt(16);
				int j = context.level().getHeight(Heightmap.Types.WORLD_SURFACE_WG, i, k) - 1;
				if (!base_blocks.contains(context.level().getBlockState(new BlockPos(i, j, k)).getBlock()))
					continue;
				BlockPos spawnTo = new BlockPos(i + 0, j + 0, k + 0);
				if (template.placeInWorld(context.level(), spawnTo, spawnTo,
						StructureHelper.createImprovedSettings(context.random()),
						context.random(), 2)) {

					// Use ORANGE fruit type for this feature
					FruitLeaveBlock.FruitType fruitType = FruitLeaveBlock.FruitType.ORANGE;

					// Fix waterlogged leaves in the area around the tree
					for (int dx = -4; dx <= 4; dx++) {
						for (int dy = 0; dy <= 8; dy++) {
							for (int dz = -4; dz <= 4; dz++) {
								BlockPos pos = BlockPos.containing(i + dx, j + dy, k + dz);
								net.minecraft.world.level.block.state.BlockState state = context.level().getBlockState(pos);

								// Check if the block is any type of leaf block
								if (state.getBlock() instanceof net.minecraft.world.level.block.LeavesBlock ||
									state.getBlock() == de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks.FRUIT_LEAVE.get()) {

									// Check if the block has the waterlogged property and is waterlogged
									if (state.hasProperty(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED) &&
										state.getValue(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED)) {

										// Set waterlogged to false
										context.level().setBlock(pos, state.setValue(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED, false), 3);
									}
								}
							}
						}
					}

					// Update all leave blocks to have the correct fruit type and randomized growth stages
					WorldGenTreeHelper.updateFruitLeaveBlocks(context.level(), i, j, k, 4, 8, fruitType);

					// Update leaf connections to prevent decay
					WorldGenTreeHelper.updateLeafConnections(context.level(), i, j, k, 4, 8);

					// Double-check that no leaves are waterlogged after all updates
					for (int dx = -4; dx <= 4; dx++) {
						for (int dy = 0; dy <= 8; dy++) {
							for (int dz = -4; dz <= 4; dz++) {
								BlockPos pos = BlockPos.containing(i + dx, j + dy, k + dz);
								net.minecraft.world.level.block.state.BlockState state = context.level().getBlockState(pos);

								// Check if the block is any type of leaf block
								if (state.getBlock() instanceof net.minecraft.world.level.block.LeavesBlock ||
									state.getBlock() == de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks.FRUIT_LEAVE.get()) {

									// Check if the block has the waterlogged property and is waterlogged
									if (state.hasProperty(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED) &&
										state.getValue(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED)) {

										// Set waterlogged to false
										context.level().setBlock(pos, state.setValue(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED, false), 3);
									}
								}
							}
						}
					}

					anyPlaced = true;
				}
			}
		}
		return anyPlaced;
	}
}

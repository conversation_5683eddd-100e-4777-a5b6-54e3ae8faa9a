
package de.sarocesch.sarosfruittreesmod.item;

import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;

public class RottenAppleItem extends Item {
	public RottenAppleItem(Properties properties) {
		super(properties.stacksTo(64).rarity(Rarity.UNCOMMON));
	}

	@Override
	public int getUseDuration(ItemStack itemstack, net.minecraft.world.entity.LivingEntity entity) {
		return 32;
	}
}

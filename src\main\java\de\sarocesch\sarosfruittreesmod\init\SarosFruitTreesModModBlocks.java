package de.sarocesch.sarosfruittreesmod.init;

import net.minecraftforge.eventbus.api.bus.BusGroup;
import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.DeferredRegister;

import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.core.registries.Registries;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;

import de.sarocesch.sarosfruittreesmod.block.WormRemoverBlock;
import de.sarocesch.sarosfruittreesmod.block.GoldenFarmlandBlock;
import de.sarocesch.sarosfruittreesmod.block.FruitSaplingBlock;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;
import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

public class SarosFruitTreesModModBlocks {
	public static final DeferredRegister<Block> REGISTRY = DeferredRegister.create(ForgeRegistries.BLOCKS, SarosFruitTreesModMod.MODID);

	// Using a single block with blockstates for all fruit saplings
	public static final RegistryObject<Block> FRUIT_SAPLING = REGISTRY.register("fruit_sapling",
		() -> new FruitSaplingBlock(BlockBehaviour.Properties.of()
			.mapColor(net.minecraft.world.level.material.MapColor.PLANT)
			.pushReaction(net.minecraft.world.level.material.PushReaction.DESTROY)
			.sound(SoundType.GRASS)
			.instabreak()
			.noCollission()
			.noOcclusion()
			.randomTicks()
			.isViewBlocking((bs, br, bp) -> false)
			.isRedstoneConductor((bs, br, bp) -> false)
			.isSuffocating((bs, br, bp) -> false)
			.setId(ResourceKey.create(Registries.BLOCK, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":fruit_sapling")))));

	// Unified leave block with blockstates for all fruit types and growth stages
	public static final RegistryObject<Block> FRUIT_LEAVE = REGISTRY.register("fruit_leave",
		() -> new FruitLeaveBlock(BlockBehaviour.Properties.of()
			.mapColor(net.minecraft.world.level.material.MapColor.PLANT)
			.sound(SoundType.GRASS)
			.strength(0.2f)
			.noOcclusion()
			.isViewBlocking((bs, br, bp) -> false)
			.isSuffocating((bs, br, bp) -> false)
			.setId(ResourceKey.create(Registries.BLOCK, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":fruit_leave")))));

	// Other blocks
	public static final RegistryObject<Block> WORM_REMOVER = REGISTRY.register("worm_remover",
		() -> new WormRemoverBlock(BlockBehaviour.Properties.of()
			.sound(SoundType.GLASS)
			.strength(0.3f)
			.noOcclusion()
			.isViewBlocking((bs, br, bp) -> false)
			.isSuffocating((bs, br, bp) -> false)
			.setId(ResourceKey.create(Registries.BLOCK, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":worm_remover")))));

	public static final RegistryObject<Block> GOLDEN_FARMLAND = REGISTRY.register("golden_farmland",
		() -> new GoldenFarmlandBlock(BlockBehaviour.Properties.of()
			.mapColor(net.minecraft.world.level.material.MapColor.DIRT)
			.sound(SoundType.GRAVEL)
			.strength(0.6f)
			.setId(ResourceKey.create(Registries.BLOCK, ResourceLocation.parse(SarosFruitTreesModMod.MODID + ":golden_farmland")))));

	// Legacy leave blocks - moved to OLD folder with .old extension
	// All leave blocks now use FRUIT_LEAVE with appropriate blockstates

	// Register method for ModLoader
	public static void register(BusGroup eventBus) {
		REGISTRY.register(eventBus);
	}
}

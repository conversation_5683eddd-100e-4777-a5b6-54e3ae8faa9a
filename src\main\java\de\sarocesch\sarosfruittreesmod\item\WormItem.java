
package de.sarocesch.sarosfruittreesmod.item;

import net.minecraft.world.level.Level;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;
import net.minecraft.world.InteractionResult;
import net.minecraft.network.chat.Component;

import de.sarocesch.sarosfruittreesmod.procedures.WormFarmProcedure;

import java.util.List;

public class WormItem extends Item {
	public WormItem(Properties properties) {
		super(properties.stacksTo(10).rarity(Rarity.RARE));
	}

	public void appendHoverText(ItemStack itemstack, Level world, List<Component> list, TooltipFlag flag) {
		list.add(Component.literal("Compost ?"));
	}

	@Override
	public InteractionResult useOn(UseOnContext context) {
		super.useOn(context);
		WormFarmProcedure.execute(context.getLevel(), context.getClickedPos().getX(), context.getClickedPos().getY(), context.getClickedPos().getZ(), context.getPlayer());
		return InteractionResult.SUCCESS;
	}
}

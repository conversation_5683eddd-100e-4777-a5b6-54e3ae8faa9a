package de.sarocesch.sarosfruittreesmod.util;

import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.core.BlockPos;
import net.minecraft.util.RandomSource;

import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;

/**
 * Helper class for world generation of trees
 */
public class WorldGenTreeHelper {

    /**
     * Updates all FruitLeave blocks in a given area to have the correct fruit type and randomized growth stages
     * @param world The world
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param centerZ Center Z coordinate
     * @param radius Radius around the center to check for leaves
     * @param height Height above the center to check for leaves
     * @param fruitType The fruit type to set
     */
    public static void updateFruitLeaveBlocks(LevelAccessor world, double centerX, double centerY, double centerZ,
                                             int radius, int height, FruitLeaveBlock.FruitType fruitType) {
        // Get the FruitLeaveBlock
        net.minecraft.world.level.block.Block fruitLeaveBlock = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get();

        // Get all possible growth stages
        FruitLeaveBlock.GrowthStage[] allStages = FruitLeaveBlock.GrowthStage.values();

        // First, fix all waterlogged leaves in the area regardless of type
        fixWaterloggedLeaves(world, centerX, centerY, centerZ, radius, height);

        // Scan the area for leaves
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dy = 0; dy <= height; dy++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    BlockPos pos = BlockPos.containing(centerX + dx, centerY + dy, centerZ + dz);
                    BlockState state = world.getBlockState(pos);

                    // Check if the block is a FruitLeaveBlock (our mod's leaves)
                    if (state.getBlock() == fruitLeaveBlock) {
                        // Only update if it's APPLE type or the same type we're setting
                        boolean shouldUpdate = true;

                        if (state.hasProperty(FruitLeaveBlock.FRUIT_TYPE)) {
                            // If it's already our custom leaf block with a fruit type set
                            FruitLeaveBlock.FruitType currentType = state.getValue(FruitLeaveBlock.FRUIT_TYPE);
                            // Only update if it's APPLE or the same type as what we're setting
                            shouldUpdate = (currentType == FruitLeaveBlock.FruitType.APPLE || currentType == fruitType);
                        }

                        if (shouldUpdate) {
                            // Randomly select a growth stage
                            FruitLeaveBlock.GrowthStage randomStage = allStages[world instanceof ServerLevel ?
                                ((ServerLevel)world).random.nextInt(allStages.length) :
                                (int)(Math.random() * allStages.length)];

                            // Create a new state with the correct fruit type and random growth stage
                            BlockState newState = fruitLeaveBlock.defaultBlockState()
                                .setValue(FruitLeaveBlock.FRUIT_TYPE, fruitType)
                                .setValue(FruitLeaveBlock.GROWTH_STAGE, randomStage);

                            // Make sure the block is not waterlogged and has correct leaf properties
                            if (newState.hasProperty(BlockStateProperties.WATERLOGGED)) {
                                newState = newState.setValue(BlockStateProperties.WATERLOGGED, false);
                            }

                            // Set distance to 1 to prevent immediate decay
                            if (newState.hasProperty(BlockStateProperties.DISTANCE)) {
                                newState = newState.setValue(BlockStateProperties.DISTANCE, 1);
                            }

                            // Set persistent to false to allow decay when logs are removed
                            if (newState.hasProperty(BlockStateProperties.PERSISTENT)) {
                                newState = newState.setValue(BlockStateProperties.PERSISTENT, false);
                            }

                            // Set the block
                            world.setBlock(pos, newState, 3);
                        }
                    }
                }
            }
        }

        // Double-check that no leaves are waterlogged after all updates
        fixWaterloggedLeaves(world, centerX, centerY, centerZ, radius, height);
    }

    /**
     * Fixes all waterlogged leaves in a given area
     * @param world The world
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param centerZ Center Z coordinate
     * @param radius Radius around the center to check for leaves
     * @param height Height above the center to check for leaves
     */
    public static void fixWaterloggedLeaves(LevelAccessor world, double centerX, double centerY, double centerZ, int radius, int height) {
        // Scan the area for leaves
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dy = 0; dy <= height; dy++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    BlockPos pos = BlockPos.containing(centerX + dx, centerY + dy, centerZ + dz);
                    BlockState state = world.getBlockState(pos);

                    // Check if the block is any type of leaf block
                    if (state.getBlock() instanceof net.minecraft.world.level.block.LeavesBlock ||
                        state.getBlock() == SarosFruitTreesModModBlocks.FRUIT_LEAVE.get()) {

                        // Check if the block has the waterlogged property and is waterlogged
                        if (state.hasProperty(BlockStateProperties.WATERLOGGED) &&
                            state.getValue(BlockStateProperties.WATERLOGGED)) {

                            // Set waterlogged to false
                            world.setBlock(pos, state.setValue(BlockStateProperties.WATERLOGGED, false), 3);
                        }
                    }
                }
            }
        }
    }

    /**
     * Updates the leaf blocks to properly connect to logs
     * This should be called after placing a tree structure to ensure leaves don't decay
     * @param world The world
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param centerZ Center Z coordinate
     * @param radius Radius around the center to check for leaves
     * @param height Height above the center to check for leaves
     */
    public static void updateLeafConnections(LevelAccessor world, double centerX, double centerY, double centerZ, int radius, int height) {
        // For each leaf block, set DISTANCE to 1 and PERSISTENT to false
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dy = 0; dy <= height; dy++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    BlockPos pos = BlockPos.containing(centerX + dx, centerY + dy, centerZ + dz);
                    BlockState state = world.getBlockState(pos);

                    // Check if the block is a leaf block
                    if (state.getBlock() == SarosFruitTreesModModBlocks.FRUIT_LEAVE.get() ||
                        state.getBlock() == net.minecraft.world.level.block.Blocks.OAK_LEAVES) {

                        // For our custom leaves, make sure they're not persistent and have correct distance
                        if (state.hasProperty(BlockStateProperties.PERSISTENT) &&
                            state.getValue(BlockStateProperties.PERSISTENT)) {
                            world.setBlock(pos, state.setValue(BlockStateProperties.PERSISTENT, false), 3);
                        }

                        // Set DISTANCE to 1 to prevent immediate decay
                        if (state.hasProperty(BlockStateProperties.DISTANCE) &&
                            state.getValue(BlockStateProperties.DISTANCE) > 1) {
                            world.setBlock(pos, state.setValue(BlockStateProperties.DISTANCE, 1), 3);
                        }
                    }
                }
            }
        }
    }

    /**
     * Gets a random tree structure name from the available structure
     * @param random The random instance
     * @return The structure name
     */
    public static String getRandomTreeStructureName(RandomSource random) {
        // All tree types use baum1.nbt to baum5.nbt
        String[] treeStructureNames = {"baum1", "baum2", "baum3", "baum4", "baum5"};
        return treeStructureNames[random.nextInt(treeStructureNames.length)];
    }

    /**
     * Gets a random fruit type for world generation (excluding GOLDEN_APPLE)
     * @param random The random instance
     * @return The fruit type (APPLE, ORANGE, or PEAR, but never GOLDEN_APPLE)
     */
    public static FruitLeaveBlock.FruitType getRandomFruitType(RandomSource random) {
        // Only use APPLE, ORANGE, and PEAR for world generation (exclude GOLDEN_APPLE)
        FruitLeaveBlock.FruitType[] allowedTypes = {
            FruitLeaveBlock.FruitType.APPLE,
            FruitLeaveBlock.FruitType.ORANGE,
            FruitLeaveBlock.FruitType.PEAR
        };
        return allowedTypes[random.nextInt(allowedTypes.length)];
    }

    /**
     * Determines the number of trees to generate in a group
     * @param random The random instance
     * @return The number of trees to generate (1, 3, or 5)
     */
    public static int getTreeGroupSize(RandomSource random) {
        // 60% chance for 1 tree, 25% chance for 3 trees, 15% chance for 5 trees
        int roll = random.nextInt(100);
        if (roll < 60) {
            return 1; // 60% chance for single tree
        } else if (roll < 85) {
            return 3; // 25% chance for 3 trees
        } else {
            return 5; // 15% chance for 5 trees
        }
    }

    /**
     * Generates an offset for a tree in a vein/cluster
     * @param random The random instance
     * @param index The index of the tree in the group
     * @param groupSize The total number of trees in the group
     * @return An array with [x, z] offsets
     */
    public static int[] getVeinOffset(RandomSource random, int index, int groupSize) {
        // Base distance between trees (minimum safety distance)
        int baseDistance = 7; // Minimum distance between trees

        // For single trees, no offset
        if (groupSize == 1) {
            return new int[] {0, 0};
        }

        // For groups, create a vein pattern
        // First tree is at center (0,0)
        if (index == 0) {
            return new int[] {0, 0};
        }

        // Direction: 0=north, 1=east, 2=south, 3=west, 4=northeast, 5=southeast, 6=southwest, 7=northwest
        int direction = random.nextInt(8);

        // Distance from center (increases with index to ensure spacing)
        int distance = baseDistance + (index * 3) + random.nextInt(4);

        // Calculate offset based on direction
        int xOffset = 0;
        int zOffset = 0;

        switch (direction) {
            case 0: // north
                zOffset = -distance;
                break;
            case 1: // east
                xOffset = distance;
                break;
            case 2: // south
                zOffset = distance;
                break;
            case 3: // west
                xOffset = -distance;
                break;
            case 4: // northeast
                xOffset = distance;
                zOffset = -distance;
                break;
            case 5: // southeast
                xOffset = distance;
                zOffset = distance;
                break;
            case 6: // southwest
                xOffset = -distance;
                zOffset = distance;
                break;
            case 7: // northwest
                xOffset = -distance;
                zOffset = -distance;
                break;
        }

        // Add some randomness to make it look more natural
        xOffset += random.nextInt(5) - 2;
        zOffset += random.nextInt(5) - 2;

        return new int[] {xOffset, zOffset};
    }
}

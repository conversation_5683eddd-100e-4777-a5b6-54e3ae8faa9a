package de.sarocesch.sarosfruittreesmod.block;

import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.LeavesBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.InteractionHand;
import net.minecraft.util.StringRepresentable;
import net.minecraft.util.RandomSource;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.core.BlockPos;
import com.mojang.serialization.Codec;
import com.mojang.serialization.MapCodec;
import net.minecraft.core.particles.ParticleTypes;

import de.sarocesch.sarosfruittreesmod.procedures.LeafHandler;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModItems;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.HitResult;

public class FruitLeaveBlock extends LeavesBlock {
    // Implement the codec method required in 1.21.5
    @Override
    public MapCodec<? extends LeavesBlock> codec() {
        return simpleCodec(FruitLeaveBlock::new);
    }
    // Define the fruit type property
    public static enum FruitType implements StringRepresentable {
        APPLE("apple"),
        GOLDEN_APPLE("golden_apple"),
        ORANGE("orange"),
        PEAR("pear");

        private final String name;

        private FruitType(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }

        @Override
        public String toString() {
            return this.name;
        }
    }

    // Define the growth stage property
    public static enum GrowthStage implements StringRepresentable {
        STAGE_1("stage_1"),
        STAGE_2("stage_2"),
        STAGE_3("stage_3");

        private final String name;

        private GrowthStage(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }

        @Override
        public String toString() {
            return this.name;
        }
    }

    // Define the properties
    public static final EnumProperty<FruitType> FRUIT_TYPE = EnumProperty.create("fruit_type", FruitType.class);
    public static final EnumProperty<GrowthStage> GROWTH_STAGE = EnumProperty.create("growth_stage", GrowthStage.class);

    public FruitLeaveBlock(BlockBehaviour.Properties properties) {
        super(0.2f, properties); // Add the required float parameter (0.2f is a typical value for leaves)

        // Set default values for the block state
        this.registerDefaultState(this.stateDefinition.any()
                .setValue(PERSISTENT, false) // Set to false to allow decay when logs are removed
                .setValue(DISTANCE, 7) // Set to 7 (invalid distance) to force update on placement
                .setValue(FRUIT_TYPE, FruitType.APPLE)
                .setValue(GROWTH_STAGE, GrowthStage.STAGE_1));
    }

    @Override
    protected void spawnFallingLeavesParticle(Level level, BlockPos pos, RandomSource randomSource) {
        // Implementation of the abstract method from LeavesBlock
        // This method is responsible for spawning leaf particles when leaves are decaying
        if (level.isClientSide && randomSource.nextInt(10) == 0) {
            BlockPos blockpos = pos.below();
            BlockState blockstate = level.getBlockState(blockpos);
            if (!blockstate.canOcclude() || !blockstate.isFaceSturdy(level, blockpos, net.minecraft.core.Direction.UP)) {
                double d0 = pos.getX() + randomSource.nextDouble();
                double d1 = pos.getY() - 0.05D;
                double d2 = pos.getZ() + randomSource.nextDouble();
                // In 1.21.5, FALLING_LEAVES was renamed to CHERRY_LEAVES
                level.addParticle(ParticleTypes.CHERRY_LEAVES, d0, d1, d2, 0.0D, 0.0D, 0.0D);
            }
        }
    }

    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        super.createBlockStateDefinition(builder);
        builder.add(FRUIT_TYPE, GROWTH_STAGE);
    }

    @Override
    public BlockState getStateForPlacement(BlockPlaceContext context) {
        // When placed by a player, set PERSISTENT to true to prevent decay
        // We can't directly call updateDistance as it's protected, so we'll use the parent implementation
        // which will set the correct DISTANCE value
        return super.getStateForPlacement(context)
                .setValue(FRUIT_TYPE, FruitType.APPLE)
                .setValue(GROWTH_STAGE, GrowthStage.STAGE_1);
    }

    public int getLightBlock(BlockState state, BlockGetter worldIn, BlockPos pos) {
        return 1;
    }

    // In Forge 1.20.4 hat sich die Methode geändert
    public ItemStack getCloneItemStack(BlockState state, HitResult target, BlockGetter world, BlockPos pos, Player player) {
        // Return the appropriate sapling based on fruit type
        FruitType fruitType = state.getValue(FRUIT_TYPE);
        if (fruitType == FruitType.APPLE || fruitType == FruitType.GOLDEN_APPLE) {
            return new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get());
        } else if (fruitType == FruitType.ORANGE) {
            return new ItemStack(SarosFruitTreesModModItems.ORANGES_SAPLINGS.get());
        } else if (fruitType == FruitType.PEAR) {
            return new ItemStack(SarosFruitTreesModModItems.PEAR_SAPLINGS.get());
        }
        return new ItemStack(Blocks.AIR);
    }

    @Override
    public void onPlace(BlockState blockstate, Level world, BlockPos pos, BlockState oldState, boolean moving) {
        super.onPlace(blockstate, world, pos, oldState, moving);
        world.scheduleTick(pos, this, 2000);
    }

    @Override
    public void randomTick(BlockState blockstate, ServerLevel world, BlockPos pos, RandomSource random) {
        // Call the parent randomTick which handles leaf decay
        super.randomTick(blockstate, world, pos, random);

        // Only handle growth if the leaf is not decaying (distance < 7)
        if (blockstate.getValue(DISTANCE) < 7) {
            // Get the current fruit type and growth stage
            FruitType fruitType = blockstate.getValue(FRUIT_TYPE);
            GrowthStage growthStage = blockstate.getValue(GROWTH_STAGE);

            // Handle random tick growth
            double progressChance = 0.1; // Default 10% chance

            // Adjust chance based on leaf type
            if (fruitType == FruitType.APPLE) {
                progressChance = 0.2; // 20% chance for apple leaves
            } else if (fruitType == FruitType.GOLDEN_APPLE) {
                progressChance = 0.2; // 20% chance for golden apple leaves
            } else if (fruitType == FruitType.ORANGE) {
                progressChance = 0.1; // 10% chance for orange leaves
            }

            // Check if we should progress
            if (random.nextFloat() < progressChance) {
                // Get the next stage
                GrowthStage nextStage = getNextStage(growthStage);

                // If there is a next stage, update the blockstate
                if (nextStage != null) {
                    world.setBlock(pos, blockstate.setValue(GROWTH_STAGE, nextStage), 3);
                }
            }
        }
    }

    @Override
    public void tick(BlockState blockstate, ServerLevel world, BlockPos pos, RandomSource random) {
        // Call super.tick which handles leaf decay logic
        super.tick(blockstate, world, pos, random);

        int x = pos.getX();
        int y = pos.getY();
        int z = pos.getZ();

        // Get the current fruit type and growth stage
        FruitType fruitType = blockstate.getValue(FRUIT_TYPE);
        GrowthStage growthStage = blockstate.getValue(GROWTH_STAGE);

        // Convert to LeafHandler types
        LeafHandler.LeafType leafType;
        LeafHandler.LeafStage leafStage;

        if (fruitType == FruitType.APPLE) {
            leafType = LeafHandler.LeafType.APPLE;
        } else if (fruitType == FruitType.GOLDEN_APPLE) {
            leafType = LeafHandler.LeafType.GOLDEN_APPLE;
        } else if (fruitType == FruitType.ORANGE) {
            leafType = LeafHandler.LeafType.ORANGE;
        } else {
            leafType = LeafHandler.LeafType.PEAR;
        }

        if (growthStage == GrowthStage.STAGE_1) {
            leafStage = LeafHandler.LeafStage.STAGE_1;
        } else if (growthStage == GrowthStage.STAGE_2) {
            leafStage = LeafHandler.LeafStage.STAGE_2;
        } else {
            leafStage = LeafHandler.LeafStage.STAGE_3;
        }

        // Only handle growth if the leaf is not decaying (distance < 7)
        if (blockstate.getValue(DISTANCE) < 7) {
            // Handle random tick growth
            double progressChance = 0.1; // Default 10% chance

            // Adjust chance based on leaf type
            if (leafType == LeafHandler.LeafType.APPLE) {
                progressChance = 0.2; // 20% chance for apple leaves
            } else if (leafType == LeafHandler.LeafType.GOLDEN_APPLE) {
                progressChance = 0.2; // 20% chance for golden apple leaves
            } else if (leafType == LeafHandler.LeafType.ORANGE) {
                progressChance = 0.1; // 10% chance for orange leaves
            } else if (leafType == LeafHandler.LeafType.PEAR) {
                progressChance = 0.15; // 15% chance for pear leaves
            }

            // Check if we should progress
            if (random.nextFloat() < progressChance) {
                // Get the next stage
                GrowthStage nextStage = getNextStage(growthStage);

                // If there is a next stage, update the blockstate
                if (nextStage != null) {
                    world.setBlock(pos, blockstate.setValue(GROWTH_STAGE, nextStage), 3);
                }
            }
        }

        // Schedule next tick
        world.scheduleTick(pos, this, 2000);
    }

    public InteractionResult use(BlockState blockstate, Level world, BlockPos pos, Player entity, InteractionHand hand, BlockHitResult hit) {
        int x = pos.getX();
        int y = pos.getY();
        int z = pos.getZ();

        // Get the current fruit type and growth stage
        FruitType fruitType = blockstate.getValue(FRUIT_TYPE);
        GrowthStage growthStage = blockstate.getValue(GROWTH_STAGE);

        // Convert to LeafHandler types
        LeafHandler.LeafType leafType;
        LeafHandler.LeafStage leafStage;

        if (fruitType == FruitType.APPLE) {
            leafType = LeafHandler.LeafType.APPLE;
        } else if (fruitType == FruitType.GOLDEN_APPLE) {
            leafType = LeafHandler.LeafType.GOLDEN_APPLE;
        } else {
            leafType = LeafHandler.LeafType.ORANGE;
        }

        if (growthStage == GrowthStage.STAGE_1) {
            leafStage = LeafHandler.LeafStage.STAGE_1;
        } else if (growthStage == GrowthStage.STAGE_2) {
            leafStage = LeafHandler.LeafStage.STAGE_2;
        } else {
            leafStage = LeafHandler.LeafStage.STAGE_3;
        }

        // Check if player is holding bonemeal
        if (entity.getItemInHand(hand).getItem() == net.minecraft.world.item.Items.BONE_MEAL) {
            // Use the unified LeafHandler for bonemeal
            LeafHandler.handleBonemeal(world, x, y, z, entity, leafType, leafStage);

            // Check if bonemeal was successful (if the block changed)
            BlockState newState = world.getBlockState(pos);
            if (newState.getBlock() != this ||
                newState.getValue(FRUIT_TYPE) != fruitType ||
                newState.getValue(GROWTH_STAGE) != growthStage) {
                // Block was changed by the LeafHandler, don't do anything else
                return InteractionResult.SUCCESS;
            }

            // If we're still here, the block wasn't changed, so try to advance the stage directly
            GrowthStage nextStage = getNextStage(growthStage);
            if (nextStage != null && Math.random() < 0.3) { // 30% chance
                world.setBlock(pos, blockstate.setValue(GROWTH_STAGE, nextStage), 3);
            }
        } else {
            // Handle harvesting based on fruit type and growth stage
            // Only allow harvesting at stage 3 (fully grown)
            if ((fruitType == FruitType.APPLE && growthStage == GrowthStage.STAGE_3) ||
                (fruitType == FruitType.GOLDEN_APPLE && growthStage == GrowthStage.STAGE_3) ||
                (fruitType == FruitType.ORANGE && growthStage == GrowthStage.STAGE_3) ||
                (fruitType == FruitType.PEAR && growthStage == GrowthStage.STAGE_3)) {

                // Use the unified LeafHandler for harvesting
                LeafHandler.handleHarvest(world, x, y, z, entity);

                // Reset to stage 1 after harvesting
                world.setBlock(pos, blockstate.setValue(GROWTH_STAGE, GrowthStage.STAGE_1), 3);
            }
        }

        return InteractionResult.SUCCESS;
    }

    /**
     * Get the next growth stage
     * @param currentStage The current growth stage
     * @return The next growth stage, or null if there is no next stage
     */
    private GrowthStage getNextStage(GrowthStage currentStage) {
        if (currentStage == GrowthStage.STAGE_1) {
            return GrowthStage.STAGE_2;
        } else if (currentStage == GrowthStage.STAGE_2) {
            return GrowthStage.STAGE_3;
        }
        return null; // No next stage
    }
}

package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.util.TreeStructureHelper;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;

/**
 * Procedure for growing pear trees from saplings.
 * This procedure is called when a pear sapling reaches its final growth stage
 * or when bonemeal is applied to a fully grown pear sapling.
 */
public class PearTreeGrowthProcedure {
    public static void execute(LevelAccessor world, double x, double y, double z) {
        if (world instanceof ServerLevel _serverworld) {
            // Get the structure name for pear trees
            String selectedName = TreeStructureHelper.getRandomTreeStructureName(_serverworld, false, false);
            StructureTemplate template = null;

            // Try the old format first (which seems to be what was used)
            template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", selectedName));

            // If that fails, try the new format
            if (template == null) {
                template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", selectedName));
            }

            // If that still fails, try each structure name as a fallback
            if (template == null) {
                String[] structureNames = {"baum1", "baum2", "baum3", "baum4", "baum5"};
                for (String name : structureNames) {
                    if (!name.equals(selectedName)) { // Skip the one we already tried
                        // Try the old format
                        template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", name));

                        // If that fails, try the new format
                        if (template == null) {
                            template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", name));
                        }

                        // If we found a template, break out of the loop
                        if (template != null) {
                            break;
                        }
                    }
                }
            }

            // If we found a template, place it
            if (template != null) {
                // Remove the sapling
                world.setBlock(new BlockPos((int) x, (int) y, (int) z), Blocks.AIR.defaultBlockState(), 3);

                // Place the structure with offset to center the tree on the sapling position
                template.placeInWorld(_serverworld, new BlockPos((int) x - 2, (int) y, (int) z - 2), new BlockPos((int) x - 2, (int) y, (int) z - 2),
                        new StructurePlaceSettings().setRotation(Rotation.NONE).setMirror(Mirror.NONE).setIgnoreEntities(false), _serverworld.random, 3);

                // Set the fruit type for all leaves in the structure to PEAR
                TreeStructureHelper.updateFruitLeaveBlocks(world, x, y, z,
                        Math.max(template.getSize().getX(), template.getSize().getZ()) / 2, // radius
                        template.getSize().getY(), // height
                        FruitLeaveBlock.FruitType.PEAR);

                // Update leaf connections to prevent decay
                TreeStructureHelper.updateLeafConnections(world, x, y, z,
                        Math.max(template.getSize().getX(), template.getSize().getZ()) / 2, // radius
                        template.getSize().getY()); // height
            }
        }
    }
}

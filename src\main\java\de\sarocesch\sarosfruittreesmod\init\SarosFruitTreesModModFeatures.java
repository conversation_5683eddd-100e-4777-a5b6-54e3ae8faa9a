package de.sarocesch.sarosfruittreesmod.init;

import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.fml.common.Mod;

import net.minecraft.world.level.levelgen.feature.Feature;

import de.sarocesch.sarosfruittreesmod.world.features.PearFeature;
import de.sarocesch.sarosfruittreesmod.world.features.Orangen4Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Orangen3Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Orangen1Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Oragen2Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Apple5Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Apple4Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Apple3Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Apple2Feature;
import de.sarocesch.sarosfruittreesmod.world.features.Apple1Feature;
import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

@Mod.EventBusSubscriber
public class SarosFruitTreesModModFeatures {
	public static final DeferredRegister<Feature<?>> REGISTRY = DeferredRegister.create(ForgeRegistries.FEATURES, SarosFruitTreesModMod.MODID);
	public static final RegistryObject<Feature<?>> APPLE_1 = REGISTRY.register("apple_1", Apple1Feature::new);
	public static final RegistryObject<Feature<?>> APPLE_2 = REGISTRY.register("apple_2", Apple2Feature::new);
	public static final RegistryObject<Feature<?>> APPLE_3 = REGISTRY.register("apple_3", Apple3Feature::new);
	public static final RegistryObject<Feature<?>> APPLE_4 = REGISTRY.register("apple_4", Apple4Feature::new);
	public static final RegistryObject<Feature<?>> APPLE_5 = REGISTRY.register("apple_5", Apple5Feature::new);
	public static final RegistryObject<Feature<?>> PEAR = REGISTRY.register("pear", PearFeature::new);
	public static final RegistryObject<Feature<?>> ORANGEN_1 = REGISTRY.register("orangen_1", Orangen1Feature::new);
	public static final RegistryObject<Feature<?>> ORAGEN_2 = REGISTRY.register("oragen_2", Oragen2Feature::new);
	public static final RegistryObject<Feature<?>> ORANGEN_3 = REGISTRY.register("orangen_3", Orangen3Feature::new);
	public static final RegistryObject<Feature<?>> ORANGEN_4 = REGISTRY.register("orangen_4", Orangen4Feature::new);
}

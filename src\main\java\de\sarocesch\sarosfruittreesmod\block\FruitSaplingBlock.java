package de.sarocesch.sarosfruittreesmod.block;

import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.level.storage.loot.LootContext;
import net.minecraft.world.level.block.state.properties.IntegerProperty;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.BonemealableBlock;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.InteractionHand;
import net.minecraft.util.RandomSource;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.core.BlockPos;
import net.minecraft.util.StringRepresentable;

import de.sarocesch.sarosfruittreesmod.procedures.AppleTreeGrowthProcedure;
import de.sarocesch.sarosfruittreesmod.procedures.GoldenAppleTreeProcedure;
import de.sarocesch.sarosfruittreesmod.procedures.OrangeTreeGrowthProcedure;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModItems;
import de.sarocesch.sarosfruittreesmod.procedures.PearTreeGrowthProcedure;

import java.util.List;
import java.util.Collections;

public class FruitSaplingBlock extends Block implements BonemealableBlock {
    // Define the stage property with values 0-4
    public static final IntegerProperty STAGE = IntegerProperty.create("stage", 0, 4);

    // Define the fruit type property
    public static enum FruitType implements StringRepresentable {
        APPLE("apple"),
        GOLDEN_APPLE("golden_apple"),
        ORANGE("orange"),
        PEAR("pear");

        private final String name;

        private FruitType(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }

        @Override
        public String toString() {
            return this.name;
        }
    }

    public static final EnumProperty<FruitType> FRUIT_TYPE = EnumProperty.create("fruit_type", FruitType.class);

    public FruitSaplingBlock(BlockBehaviour.Properties properties) {
        super(properties);
        // Set default stage to 0 and fruit type to APPLE
        this.registerDefaultState(this.stateDefinition.any()
            .setValue(STAGE, 0)
            .setValue(FRUIT_TYPE, FruitType.APPLE));
    }

    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        builder.add(STAGE, FRUIT_TYPE);
    }

    @Override
    public VoxelShape getShape(BlockState state, BlockGetter world, BlockPos pos, CollisionContext context) {
        int stage = state.getValue(STAGE);
        if (stage == 0) {
            return box(5, 0, 5, 11, 5, 11);
        } else if (stage == 1) {
            return box(5, 0, 5, 11, 7, 11);
        } else if (stage == 2) {
            return box(4, 0, 4, 12, 9, 12);
        } else if (stage == 3) {
            return box(3, 0, 3, 13, 10, 13);
        } else {
            return box(2, 0, 2, 14, 12, 14);
        }
    }

    // In Forge 1.20.4 hat sich die Methode geändert
    public ItemStack getCloneItemStack(BlockState state, HitResult target, BlockGetter world, BlockPos pos, Player player) {
        FruitType fruitType = state.getValue(FRUIT_TYPE);
        if (fruitType == FruitType.APPLE) {
            return new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get());
        } else if (fruitType == FruitType.GOLDEN_APPLE) {
            return new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get()); // Use the correct item here
        } else if (fruitType == FruitType.ORANGE) {
            return new ItemStack(SarosFruitTreesModModItems.ORANGES_SAPLINGS.get());
        } else if (fruitType == FruitType.PEAR) {
            return new ItemStack(SarosFruitTreesModModItems.PEAR_SAPLINGS.get());
        }
        return new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get());
    }

    public List<ItemStack> getDrops(BlockState state, net.minecraft.world.level.storage.loot.LootParams.Builder builder) {
        List<ItemStack> dropsOriginal = super.getDrops(state, builder);
        if (!dropsOriginal.isEmpty())
            return dropsOriginal;

        FruitType fruitType = state.getValue(FRUIT_TYPE);
        if (fruitType == FruitType.APPLE) {
            return Collections.singletonList(new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get()));
        } else if (fruitType == FruitType.GOLDEN_APPLE) {
            return Collections.singletonList(new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get())); // Golden apple saplings use the same item
        } else if (fruitType == FruitType.ORANGE) {
            return Collections.singletonList(new ItemStack(SarosFruitTreesModModItems.ORANGES_SAPLINGS.get()));
        } else if (fruitType == FruitType.PEAR) {
            return Collections.singletonList(new ItemStack(SarosFruitTreesModModItems.PEAR_SAPLINGS.get()));
        }
        return Collections.singletonList(new ItemStack(SarosFruitTreesModModItems.APPLE_SAPLINGS.get()));
    }

    @Override
    public void randomTick(BlockState blockstate, ServerLevel world, BlockPos pos, RandomSource random) {
        super.randomTick(blockstate, world, pos, random);
        int x = pos.getX();
        int y = pos.getY();
        int z = pos.getZ();

        // Get the current stage and fruit type
        int stage = blockstate.getValue(STAGE);
        FruitType fruitType = blockstate.getValue(FRUIT_TYPE);

        // Handle growth based on stage
        if (stage < 4) {
            // 10% chance to grow to next stage (affected by random tick speed)
            if (random.nextFloat() < 0.1f) {
                world.setBlock(pos, blockstate.setValue(STAGE, stage + 1), 3);
            }
        } else if (stage == 4) {
            // Final stage - chance to grow into a tree
            if (random.nextFloat() < 0.1f) {
                // Call the appropriate procedure based on fruit type
                if (fruitType == FruitType.APPLE) {
                    AppleTreeGrowthProcedure.execute(world, x, y, z);
                } else if (fruitType == FruitType.GOLDEN_APPLE) {
                    GoldenAppleTreeProcedure.growTree(world, x, y, z);
                } else if (fruitType == FruitType.ORANGE) {
                    // Call the appropriate procedure for orange trees
                    OrangeTreeGrowthProcedure.execute(world, x, y, z);
                } else if (fruitType == FruitType.PEAR) {
                    // Use the specific procedure for pear trees
                    PearTreeGrowthProcedure.execute(world, x, y, z);
                }
            }
        }
    }

    public InteractionResult use(BlockState blockstate, Level world, BlockPos pos, Player entity, InteractionHand hand, BlockHitResult hit) {
        ItemStack itemstack = entity.getItemInHand(hand);

        // Check if player is holding bonemeal
        if (itemstack.getItem() == Items.BONE_MEAL) {
            // Check if bonemeal can be applied
            if (isValidBonemealTarget(world, pos, blockstate)) {
                if (world instanceof ServerLevel) {
                    // Always show particles
                    world.levelEvent(1505, pos, 0);

                    // Apply bonemeal effect if successful
                    if (isBonemealSuccess(world, world.random, pos, blockstate)) {
                        performBonemeal((ServerLevel) world, world.random, pos, blockstate);
                    }

                    // Always consume bonemeal
                    if (!entity.getAbilities().instabuild) {
                        itemstack.shrink(1);
                    }
                }
                return world.isClientSide ? InteractionResult.SUCCESS : InteractionResult.CONSUME;
            }
        }

        return InteractionResult.PASS;
    }

    @Override
    public boolean isValidBonemealTarget(LevelReader worldIn, BlockPos pos, BlockState state) {
        // Allow bonemeal on all stages including stage 4
        return true;
    }

    @Override
    public boolean isBonemealSuccess(Level worldIn, RandomSource rand, BlockPos pos, BlockState state) {
        // 30% chance for bonemeal to succeed
        return rand.nextFloat() < 0.3f;
    }

    @Override
    public void performBonemeal(ServerLevel worldIn, RandomSource rand, BlockPos pos, BlockState state) {
        int currentStage = state.getValue(STAGE);
        FruitType fruitType = state.getValue(FRUIT_TYPE);

        if (currentStage < 4) {
            // Advance to next stage
            worldIn.setBlock(pos, state.setValue(STAGE, currentStage + 1), 3);
        } else if (currentStage == 4) {
            // At stage 4, spawn the tree structure based on fruit type
            if (fruitType == FruitType.APPLE) {
                AppleTreeGrowthProcedure.execute(worldIn, pos.getX(), pos.getY(), pos.getZ());
            } else if (fruitType == FruitType.GOLDEN_APPLE) {
                GoldenAppleTreeProcedure.growTree(worldIn, pos.getX(), pos.getY(), pos.getZ());
            } else if (fruitType == FruitType.ORANGE) {
                // Call the appropriate procedure for orange trees
                OrangeTreeGrowthProcedure.execute(worldIn, pos.getX(), pos.getY(), pos.getZ());
            } else if (fruitType == FruitType.PEAR) {
                // Use the specific procedure for pear trees
                PearTreeGrowthProcedure.execute(worldIn, pos.getX(), pos.getY(), pos.getZ());
            }
        }
    }
}

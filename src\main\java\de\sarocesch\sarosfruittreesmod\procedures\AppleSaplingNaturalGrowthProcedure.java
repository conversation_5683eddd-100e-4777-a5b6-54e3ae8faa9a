package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.Level;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;

import java.util.Map;

/**
 * Procedure for handling the natural growth of apple saplings.
 * This procedure is called when an apple sapling updates its tick.
 */
public class AppleSaplingNaturalGrowthProcedure {
    public static void execute(LevelAccessor world, double x, double y, double z) {
        // Tree growth logic - no additional random check needed as it's already handled in the block's randomTick method
        // Remove the sapling
        world.setBlock(BlockPos.containing(x, y, z), Blocks.AIR.defaultBlockState(), 3);

        // Place the tree structure
        if (world instanceof ServerLevel _serverworld) {
            // Try different structure names with different formats
            StructureTemplate template = null;
            String[] structureNames = {"baum", "baum1", "baum2", "baum3", "baum4", "baum5"};

            // Try each structure name until we find one that works
            for (String name : structureNames) {
                // Try the new format first
                template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.tryParse("saros_fruit_trees_mod:" + name));

                // If that fails, try the old format
                if (template == null) {
                    template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.tryParse("saros_fruit_trees_mod:" + name));
                }

                // If we found a template, break out of the loop
                if (template != null) {
                    break;
                }
            }

            // If we have a template, place it
            if (template != null) {
                template.placeInWorld(_serverworld, BlockPos.containing(x - 2, y, z - 2), BlockPos.containing(x - 2, y, z - 2),
                        new StructurePlaceSettings().setRotation(Rotation.NONE).setMirror(Mirror.NONE).setIgnoreEntities(false),
                        _serverworld.random, 3);
            }
        }

        // Set the block below to grass block
        {
            BlockPos _bp = BlockPos.containing(x, y - 1, z);
            BlockState _bs = Blocks.GRASS_BLOCK.defaultBlockState();
            BlockState _bso = world.getBlockState(_bp);
            for (Map.Entry<Property<?>, Comparable<?>> entry : _bso.getValues().entrySet()) {
                Property _property = _bs.getBlock().getStateDefinition().getProperty(entry.getKey().getName());
                if (_property != null && _bs.getValue(_property) != null)
                    try {
                        _bs = _bs.setValue(_property, (Comparable) entry.getValue());
                    } catch (Exception e) {
                    }
            }
            world.setBlock(_bp, _bs, 3);
        }
    }
}

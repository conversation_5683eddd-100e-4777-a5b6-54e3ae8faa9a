
package de.sarocesch.sarosfruittreesmod.item;

import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.Level;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.network.chat.Component;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;

import java.util.List;

import com.google.common.collect.Multimap;
import com.google.common.collect.ImmutableMultimap;

public class LeafRemoverItem extends Item {
	public LeafRemoverItem(Properties properties) {
		super(properties.durability(1000));
	}

	@Override
	public float getDestroySpeed(ItemStack itemstack, BlockState blockstate) {
		// Check if it's the FruitLeaveBlock
		if (blockstate.getBlock() instanceof FruitLeaveBlock) {
			return 100f;
		}

		return 1;
	}

	@Override
	public boolean mineBlock(ItemStack itemstack, Level world, BlockState blockstate, BlockPos pos, LivingEntity entity) {
		itemstack.hurtAndBreak(1, entity, EquipmentSlot.MAINHAND);
		return true;
	}

	@Override
	public void hurtEnemy(ItemStack itemstack, LivingEntity entity, LivingEntity sourceentity) {
		itemstack.hurtAndBreak(2, entity, EquipmentSlot.MAINHAND);
	}

	public int getEnchantmentValue() {
		return 5;
	}

	public Multimap<Attribute, AttributeModifier> getDefaultAttributeModifiers(EquipmentSlot equipmentSlot) {
		return ImmutableMultimap.of();
	}

	public void appendHoverText(ItemStack itemstack, Level world, List<Component> list, TooltipFlag flag) {
		list.add(Component.literal("Easily removes the leaves from fruit trees"));
	}
}

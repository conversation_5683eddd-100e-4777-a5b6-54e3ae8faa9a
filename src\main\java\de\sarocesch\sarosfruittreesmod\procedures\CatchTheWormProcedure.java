package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.Vec2;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.Entity;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.network.chat.Component;
import net.minecraft.core.BlockPos;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.CommandSource;

import de.sarocesch.sarosfruittreesmod.util.WormRemoverHelper;

public class CatchTheWormProcedure {
	public static void execute(LevelAccessor world, double x, double y, double z, Entity entity) {
		if (entity == null)
			return;

		// Reset the worm remover block to stage 0
		BlockPos pos = BlockPos.containing(x, y, z);
		WormRemoverHelper.setWormRemoverStage(world, pos, 0);

		// Give rewards
		if (world instanceof ServerLevel serverLevel) {
			CommandSourceStack source = new CommandSourceStack(
				CommandSource.NULL,
				new Vec3(x, y, z),
				Vec2.ZERO,
				serverLevel,
				4,
				"",
				Component.literal(""),
				serverLevel.getServer(),
				null
			).withSuppressedOutput();

			serverLevel.getServer().getCommands().performPrefixedCommand(source, "give @p dirt 2");
			serverLevel.getServer().getCommands().performPrefixedCommand(source, "give @p saros_fruit_trees_mod:worm");
			serverLevel.getServer().getCommands().performPrefixedCommand(source, "give @p minecraft:bone_meal");
		}

		if (entity instanceof Player player) {
			player.closeContainer();
			player.displayClientMessage(Component.translatable("block.saros_fruit_trees_mod.worm_remover.caught_worm"), false);
		}
	}
}

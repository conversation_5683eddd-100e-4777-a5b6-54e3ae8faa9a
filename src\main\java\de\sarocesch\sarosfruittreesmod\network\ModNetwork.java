package de.sarocesch.sarosfruittreesmod.network;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.ChannelBuilder;
import net.minecraftforge.network.NetworkDirection;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.SimpleChannel;

import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

public class ModNetwork {
    // Netzwerk-Kanal für Forge 1.20.4
    public static final SimpleChannel CHANNEL = ChannelBuilder
            .named(ResourceLocation.fromNamespaceAndPath(SarosFruitTreesModMod.MODID, "main"))
            .networkProtocolVersion(1)
            .clientAcceptedVersions((status, version) -> true)
            .serverAcceptedVersions((status, version) -> true)
            .simpleChannel();

    public static void register() {
        // Registriere die WormGUIButtonMessage mit der korrekten Forge 1.20.4 API
        // Explizit die Richtung angeben (Client -> Server)
        CHANNEL.messageBuilder(WormGUIButtonMessage.class, NetworkDirection.PLAY_TO_SERVER)
                .decoder(WormGUIButtonMessage::new)
                .encoder(WormGUIButtonMessage::write)
                .consumerMainThread((msg, ctx) -> {
                    ServerPlayer sender = ctx.getSender();
                    if (sender != null) {
                        WormGUIButtonMessage.handle(msg, sender);
                    } else {
                        SarosFruitTreesModMod.LOGGER.error("Received WormGUIButtonMessage but sender is null!");
                    }
                    // Wichtig: Kontext als bearbeitet markieren
                    ctx.setPacketHandled(true);
                })
                .add();
    }

    public static void sendToServer(WormGUIButtonMessage message) {
        CHANNEL.send(message, PacketDistributor.SERVER.noArg());
    }
}

package de.sarocesch.sarosfruittreesmod.init;

import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.common.extensions.IForgeMenuType;

import net.minecraft.world.inventory.MenuType;

import de.sarocesch.sarosfruittreesmod.world.inventory.WormGUIMenu;
import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

public class SarosFruitTreesModModMenus {
	public static final DeferredRegister<MenuType<?>> REGISTRY = DeferredRegister.create(ForgeRegistries.MENU_TYPES, SarosFruitTreesModMod.MODID);
	public static final RegistryObject<MenuType<WormGUIMenu>> WORM_GUI = REGISTRY.register("worm_gui", () -> IForgeMenuType.create(WormGUIMenu::new));
	// Registrierung erfolgt jetzt in der Hauptklasse, keine register-Methode mehr nötig
}


package de.sarocesch.sarosfruittreesmod.item;

import net.minecraft.world.level.Level;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;
import net.minecraft.world.InteractionResult;
import net.minecraft.network.chat.Component;

import de.sarocesch.sarosfruittreesmod.procedures.PearSaplingPlacementProcedure;

import java.util.List;

public class PearSaplingsItem extends Item {
	public PearSaplingsItem(Properties properties) {
		super(properties.stacksTo(64).rarity(Rarity.COMMON));
	}

	public void appendHoverText(ItemStack itemstack, Level world, List<Component> list, TooltipFlag flag) {
		list.add(Component.literal("Pear Sapling"));
	}

	@Override
	public InteractionResult useOn(UseOnContext context) {
		super.useOn(context);
		PearSaplingPlacementProcedure.execute(context.getLevel(), context.getClickedPos().getX(), context.getClickedPos().getY(), context.getClickedPos().getZ(), context.getPlayer());
		return InteractionResult.SUCCESS;
	}
}

package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;
import net.minecraft.world.level.levelgen.structure.templatesystem.BlockIgnoreProcessor;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.LeavesBlock;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.BoneMealItem;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.BlockPos;
import net.minecraft.client.Minecraft;

import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;
import de.sarocesch.sarosfruittreesmod.block.FruitSaplingBlock;
import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;
import de.sarocesch.sarosfruittreesmod.util.TreeStructureHelper;

import java.util.Map;

/**
 * Consolidated procedure for handling Golden Apple tree functionality.
 * This class combines the functionality of:
 * - GAppleSaplingUpdateTickProcedure
 * - GAppleSaplingOnBlockRightClickedProcedure
 * - GAppleSapling4UpdateTickProcedure
 * - GAppleSapling4OnBlockRightClickedProcedure
 */
public class GoldenAppleTreeProcedure {

    /**
     * Main method to grow a golden apple tree from a sapling
     */
    public static void growTree(LevelAccessor world, double x, double y, double z) {
        // First remove the sapling block
        world.setBlock(BlockPos.containing(x, y, z), Blocks.AIR.defaultBlockState(), 3);

        if (world instanceof ServerLevel _serverworld) {
            // Get the structure name for golden apple trees
            String selectedName = TreeStructureHelper.getRandomTreeStructureName(_serverworld, true, false);
            StructureTemplate template = null;

            // Try the old format first (which seems to be what was used)
            template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", selectedName));

            // If that fails, try the new format
            if (template == null) {
                template = _serverworld.getStructureManager().getOrCreate(ResourceLocation.fromNamespaceAndPath("saros_fruit_trees_mod", selectedName));
            }

            // If we have a template, place it
            if (template != null) {
                // Place the structure
                template.placeInWorld(_serverworld, BlockPos.containing(x - 2, y, z - 2), BlockPos.containing(x - 2, y, z - 2),
                        new StructurePlaceSettings()
                            .setRotation(Rotation.NONE)
                            .setMirror(Mirror.NONE)
                            .setIgnoreEntities(false)
                            .addProcessor(BlockIgnoreProcessor.STRUCTURE_BLOCK), // Ignore structure blocks
                        _serverworld.random, 3);

                // Fix waterlogged leaves in the area around the tree
                for (int dx = -4; dx <= 4; dx++) {
                    for (int dy = 0; dy <= 8; dy++) {
                        for (int dz = -4; dz <= 4; dz++) {
                            BlockPos pos = BlockPos.containing(x + dx, y + dy, z + dz);
                            BlockState state = world.getBlockState(pos);

                            // Check if the block is any type of leaf block
                            if (state.getBlock() instanceof LeavesBlock ||
                                state.getBlock() == SarosFruitTreesModModBlocks.FRUIT_LEAVE.get()) {

                                // Check if the block has the waterlogged property and is waterlogged
                                if (state.hasProperty(BlockStateProperties.WATERLOGGED) &&
                                    state.getValue(BlockStateProperties.WATERLOGGED)) {

                                    // Set waterlogged to false
                                    world.setBlock(pos, state.setValue(BlockStateProperties.WATERLOGGED, false), 3);
                                }
                            }
                        }
                    }
                }

                // Update all leave blocks to have the correct fruit type and randomized growth stages
                TreeStructureHelper.updateFruitLeaveBlocks(world, x, y, z, 4, 8,
                    FruitLeaveBlock.FruitType.GOLDEN_APPLE);

                // Update leaf connections to prevent decay
                TreeStructureHelper.updateLeafConnections(world, x, y, z, 4, 8);

                // Set the block below to grass block
                {
                    BlockPos _bp = BlockPos.containing(x, y - 1, z);
                    BlockState _bs = Blocks.GRASS_BLOCK.defaultBlockState();
                    BlockState _bso = world.getBlockState(_bp);
                    for (Map.Entry<Property<?>, Comparable<?>> entry : _bso.getValues().entrySet()) {
                        Property _property = _bs.getBlock().getStateDefinition().getProperty(entry.getKey().getName());
                        if (_property != null && _bs.getValue(_property) != null)
                            try {
                                _bs = _bs.setValue(_property, (Comparable) entry.getValue());
                            } catch (Exception e) {
                            }
                    }
                    world.setBlock(_bp, _bs, 3);
                }
            } else {
                // If no template was found, just place a basic oak tree as fallback
                _serverworld.setBlock(BlockPos.containing(x, y, z), Blocks.OAK_SAPLING.defaultBlockState(), 3);
                // Use bonemeal to grow the tree
                if (_serverworld.getBlockState(BlockPos.containing(x, y, z)).getBlock() == Blocks.OAK_SAPLING) {
                    BoneMealItem.applyBonemeal(new ItemStack(Items.BONE_MEAL), _serverworld, BlockPos.containing(x, y, z), null);
                }
            }
        }
    }

    /**
     * Handle bonemeal application on golden apple saplings
     */
    public static void handleBonemeal(LevelAccessor world, double x, double y, double z, Entity entity) {
        if (entity == null)
            return;

        if ((entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem() == Items.BONE_MEAL) {
            // Show particles
            if (world instanceof ServerLevel _level)
                _level.sendParticles(ParticleTypes.HAPPY_VILLAGER, x, y, z, 5, 1, 1, 1, 1);

            // Consume bonemeal if not in creative mode
            if (!isCreativeMode(entity)) {
                (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
            }

            // 30% chance for bonemeal to succeed
            if (Math.random() < 0.3) {
                // 10% chance to grow the tree immediately
                if (Math.random() < 0.1) {
                    growTree(world, x, y, z);
                } else {
                    // Otherwise advance the stage
                    BlockState currentState = world.getBlockState(BlockPos.containing(x, y, z));
                    if (currentState.getBlock() == SarosFruitTreesModModBlocks.FRUIT_SAPLING.get()) {
                        int currentStage = currentState.getValue(FruitSaplingBlock.STAGE);
                        if (currentStage < 4) {
                            world.setBlock(BlockPos.containing(x, y, z),
                                currentState.setValue(FruitSaplingBlock.STAGE, currentStage + 1), 3);
                        } else {
                            // At max stage, grow the tree
                            growTree(world, x, y, z);
                        }
                    }
                }
            }
        }
    }

    /**
     * Helper method to check if an entity is in creative mode
     */
    private static boolean isCreativeMode(Entity entity) {
        if (entity instanceof ServerPlayer _serverPlayer) {
            return _serverPlayer.gameMode.getGameModeForPlayer() == net.minecraft.world.level.GameType.CREATIVE;
        } else if (entity.level().isClientSide() && entity instanceof Player _player) {
            return Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()) != null
                    && Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()).getGameMode() == net.minecraft.world.level.GameType.CREATIVE;
        }
        return false;
    }

    /**
     * Update the block state for a golden apple sapling
     */
    public static void updateBlockState(LevelAccessor world, double x, double y, double z) {
        BlockPos pos = BlockPos.containing(x, y, z);
        BlockState _bs = SarosFruitTreesModModBlocks.FRUIT_SAPLING.get().defaultBlockState()
            .setValue(FruitSaplingBlock.FRUIT_TYPE, FruitSaplingBlock.FruitType.GOLDEN_APPLE);

        // Preserve other properties from the current block state
        BlockState _bso = world.getBlockState(pos);
        for (Map.Entry<Property<?>, Comparable<?>> entry : _bso.getValues().entrySet()) {
            Property _property = _bs.getBlock().getStateDefinition().getProperty(entry.getKey().getName());
            if (_property != null && _bs.getValue(_property) != null)
                try {
                    _bs = _bs.setValue(_property, (Comparable) entry.getValue());
                } catch (Exception e) {
                }
        }

        world.setBlock(pos, _bs, 3);
    }
}

package de.sarocesch.sarosfruittreesmod.procedures;

import net.minecraftforge.registries.ForgeRegistries;

import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.GameType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity;
import net.minecraft.sounds.SoundSource;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.core.BlockPos;
import net.minecraft.client.Minecraft;

import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;
import de.sarocesch.sarosfruittreesmod.block.FruitSaplingBlock;

/**
 * Procedure for placing apple and golden apple saplings.
 * This procedure is called when a player tries to place an apple or golden apple sapling.
 */
public class AppleSaplingPlacementProcedure {
	public static void execute(LevelAccessor world, double x, double y, double z, Entity entity) {
		if (entity == null)
			return;
		if ((world.getBlockState(BlockPos.containing(x, y, z))).getBlock() == Blocks.GRASS_BLOCK) {
			if ((world.getBlockState(BlockPos.containing(x, y + 1, z))).getBlock() == Blocks.AIR) {
				world.setBlock(BlockPos.containing(x, y + 1, z), SarosFruitTreesModModBlocks.FRUIT_SAPLING.get().defaultBlockState().setValue(FruitSaplingBlock.STAGE, 0).setValue(FruitSaplingBlock.FRUIT_TYPE, FruitSaplingBlock.FruitType.APPLE), 3);
				if (world instanceof Level _level) {
					if (!_level.isClientSide()) {
						_level.playSound(null, BlockPos.containing(x, y, z), ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.tryParse("minecraft:block.crop.break")), SoundSource.BLOCKS, 1, 1);
					} else {
						_level.playLocalSound(x, y, z, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.tryParse("minecraft:block.crop.break")), SoundSource.BLOCKS, 1, 1, false);
					}
				}
				if (new Object() {
					public boolean checkGamemode(Entity _ent) {
						if (_ent instanceof ServerPlayer _serverPlayer) {
							return _serverPlayer.gameMode.getGameModeForPlayer() == GameType.CREATIVE;
						} else if (_ent.level().isClientSide() && _ent instanceof Player _player) {
							return Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()) != null
									&& Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()).getGameMode() == GameType.CREATIVE;
						}
						return false;
					}
				}.checkGamemode(entity)) {
					world.setBlock(BlockPos.containing(x, y + 1, z), SarosFruitTreesModModBlocks.FRUIT_SAPLING.get().defaultBlockState().setValue(FruitSaplingBlock.STAGE, 0).setValue(FruitSaplingBlock.FRUIT_TYPE, FruitSaplingBlock.FruitType.APPLE), 3);
				} else {
					(entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
				}
			}
		} else if ((world.getBlockState(BlockPos.containing(x, y, z))).getBlock() == SarosFruitTreesModModBlocks.GOLDEN_FARMLAND.get()) {
			if ((world.getBlockState(BlockPos.containing(x, y + 1, z))).getBlock() == Blocks.AIR) {
				world.setBlock(BlockPos.containing(x, y + 1, z), SarosFruitTreesModModBlocks.FRUIT_SAPLING.get().defaultBlockState().setValue(FruitSaplingBlock.STAGE, 0).setValue(FruitSaplingBlock.FRUIT_TYPE, FruitSaplingBlock.FruitType.GOLDEN_APPLE), 3);
				if (world instanceof Level _level) {
					if (!_level.isClientSide()) {
						_level.playSound(null, BlockPos.containing(x, y, z), ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.tryParse("minecraft:block.crop.break")), SoundSource.BLOCKS, 1, 1);
					} else {
						_level.playLocalSound(x, y, z, ForgeRegistries.SOUND_EVENTS.getValue(ResourceLocation.tryParse("minecraft:block.crop.break")), SoundSource.BLOCKS, 1, 1, false);
					}
				}
				if (new Object() {
					public boolean checkGamemode(Entity _ent) {
						if (_ent instanceof ServerPlayer _serverPlayer) {
							return _serverPlayer.gameMode.getGameModeForPlayer() == GameType.CREATIVE;
						} else if (_ent.level().isClientSide() && _ent instanceof Player _player) {
							return Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()) != null
									&& Minecraft.getInstance().getConnection().getPlayerInfo(_player.getGameProfile().getId()).getGameMode() == GameType.CREATIVE;
						}
						return false;
					}
				}.checkGamemode(entity)) {
					world.setBlock(BlockPos.containing(x, y + 1, z), SarosFruitTreesModModBlocks.FRUIT_SAPLING.get().defaultBlockState().setValue(FruitSaplingBlock.STAGE, 0).setValue(FruitSaplingBlock.FRUIT_TYPE, FruitSaplingBlock.FruitType.GOLDEN_APPLE), 3);
				} else {
					(entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).shrink(1);
				}
			}
		}
	}
}

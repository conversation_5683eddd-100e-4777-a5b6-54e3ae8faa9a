package de.sarocesch.sarosfruittreesmod.network;

import net.minecraft.core.BlockPos;
import net.minecraft.network.FriendlyByteBuf;


import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

import de.sarocesch.sarosfruittreesmod.procedures.CatchTheWormProcedure;
import de.sarocesch.sarosfruittreesmod.world.inventory.WormGUIMenu;
import de.sarocesch.sarosfruittreesmod.SarosFruitTreesModMod;

import java.util.HashMap;

public class WormGUIButtonMessage {
    private final int buttonID;
    private final BlockPos pos;

    public static final ResourceLocation ID = ResourceLocation.fromNamespaceAndPath(SarosFruitTreesModMod.MODID, "worm_button_message");

    public WormGUIButtonMessage(int buttonID, int x, int y, int z) {
        this.buttonID = buttonID;
        this.pos = new BlockPos(x, y, z);
    }

    public WormGUIButtonMessage(FriendlyByteBuf buffer) {
        this.buttonID = buffer.readInt();
        this.pos = buffer.readBlockPos();
    }

    public void write(FriendlyByteBuf buffer) {
        buffer.writeInt(buttonID);
        buffer.writeBlockPos(pos);
    }

    public static void handle(WormGUIButtonMessage message, ServerPlayer player) {
        if (player != null) {
            Level world = player.level();
            HashMap<String, Object> guistate = WormGUIMenu.guistate;

            // Sicherheitsprüfung, ob der Chunk geladen ist
            if (!world.hasChunkAt(message.pos))
                return;

            if (message.buttonID == 0) {
                // Handle Button-Click
                handleButtonAction(player, message.buttonID, message.pos.getX(), message.pos.getY(), message.pos.getZ());
            }
        } else {
            SarosFruitTreesModMod.LOGGER.error("Player is null when handling WormGUIButtonMessage!");
        }
    }

    public static void handleButtonAction(Player entity, int buttonID, int x, int y, int z) {
        if (entity == null)
            return;

        Level world = entity.level();
        HashMap guistate = WormGUIMenu.guistate;
        // security measure to prevent arbitrary chunk generation
        if (!world.hasChunkAt(new BlockPos(x, y, z)))
            return;
        if (buttonID == 0) {
            CatchTheWormProcedure.execute(world, x, y, z, entity);
        }
    }

    // Diese Methode wird nicht mehr benötigt, da die Registrierung in ModNetwork.java erfolgt
    // Die alte Implementierung bleibt als Referenz erhalten
    /*
    public static void register(SimpleChannel channel) {
        channel.messageBuilder(WormGUIButtonMessage.class, 0, NetworkDirection.PLAY_TO_SERVER)
            .encoder(WormGUIButtonMessage::write)
            .decoder(WormGUIButtonMessage::new)
            .consumerMainThread((msg, ctx) -> msg.handle(ctx))
            .add();
    }
    */
}

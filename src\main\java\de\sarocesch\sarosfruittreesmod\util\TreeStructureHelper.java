package de.sarocesch.sarosfruittreesmod.util;

import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosfruittreesmod.block.FruitLeaveBlock;
import de.sarocesch.sarosfruittreesmod.init.SarosFruitTreesModModBlocks;

/**
 * Helper class for tree structure related operations
 */
public class TreeStructureHelper {

    /**
     * Updates all FruitLeave blocks in a given area to have the correct fruit type and randomized growth stages
     * @param world The world
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param centerZ Center Z coordinate
     * @param radius Radius around the center to check for leaves
     * @param height Height above the center to check for leaves
     * @param fruitType The fruit type to set
     */
    public static void updateFruitLeaveBlocks(LevelAccessor world, double centerX, double centerY, double centerZ,
                                             int radius, int height, FruitLeaveBlock.FruitType fruitType) {
        // Get the FruitLeaveBlock
        Block fruitLeaveBlock = SarosFruitTreesModModBlocks.FRUIT_LEAVE.get();

        // Get all possible growth stages
        FruitLeaveBlock.GrowthStage[] allStages = FruitLeaveBlock.GrowthStage.values();

        // Scan the area for leaves
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dy = 0; dy <= height; dy++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    BlockPos pos = BlockPos.containing(centerX + dx, centerY + dy, centerZ + dz);
                    BlockState state = world.getBlockState(pos);

                    // Check if the block is a FruitLeaveBlock (only our mod's leaves)
                    if (state.getBlock() == fruitLeaveBlock) {

                        // Only update if it's not our custom FruitLeaveBlock with a non-apple type
                        boolean shouldUpdate = true;

                        if (state.getBlock() == fruitLeaveBlock && state.hasProperty(FruitLeaveBlock.FRUIT_TYPE)) {
                            // If it's already our custom leaf block with a fruit type set
                            FruitLeaveBlock.FruitType currentType = state.getValue(FruitLeaveBlock.FRUIT_TYPE);
                            // Only update if it's APPLE or the same type as what we're setting
                            shouldUpdate = (currentType == FruitLeaveBlock.FruitType.APPLE || currentType == fruitType);
                        }

                        if (shouldUpdate) {
                            // Randomly select a growth stage
                            FruitLeaveBlock.GrowthStage randomStage = allStages[world instanceof ServerLevel ?
                                ((ServerLevel)world).random.nextInt(allStages.length) :
                                (int)(Math.random() * allStages.length)];

                            // Create a new state with the correct fruit type and random growth stage
                            BlockState newState = fruitLeaveBlock.defaultBlockState()
                                .setValue(FruitLeaveBlock.FRUIT_TYPE, fruitType)
                                .setValue(FruitLeaveBlock.GROWTH_STAGE, randomStage)
                                // Set PERSISTENT to false to allow decay when logs are removed
                                .setValue(net.minecraft.world.level.block.LeavesBlock.PERSISTENT, false)
                                // Set DISTANCE to 1 since these leaves are part of a tree structure and close to logs
                                .setValue(net.minecraft.world.level.block.LeavesBlock.DISTANCE, 1);

                            // Make sure the block is not waterlogged
                            if (newState.hasProperty(BlockStateProperties.WATERLOGGED)) {
                                newState = newState.setValue(BlockStateProperties.WATERLOGGED, false);
                            }

                            // Set the block
                            world.setBlock(pos, newState, 3);
                        }

                        // After setting all leaves, we should update their DISTANCE values
                        // This is done automatically by the vanilla leaf update system
                    }
                }
            }
        }
    }

    /**
     * Updates the leaf blocks to properly connect to logs
     * This should be called after placing a tree structure to ensure leaves don't decay
     * @param world The world
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param centerZ Center Z coordinate
     * @param radius Radius around the center to check for leaves
     * @param height Height above the center to check for leaves
     */
    public static void updateLeafConnections(LevelAccessor world, double centerX, double centerY, double centerZ, int radius, int height) {
        // First, find all log blocks in the area
        java.util.Set<BlockPos> logPositions = new java.util.HashSet<>();

        for (int dx = -radius; dx <= radius; dx++) {
            for (int dy = 0; dy <= height; dy++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    BlockPos pos = BlockPos.containing(centerX + dx, centerY + dy, centerZ + dz);
                    BlockState state = world.getBlockState(pos);

                    // Check if the block is a log
                    if (state.getBlock() == net.minecraft.world.level.block.Blocks.OAK_LOG) {
                        logPositions.add(pos);
                    }
                }
            }
        }

        // If we found logs, update the leaves
        if (!logPositions.isEmpty()) {
            // Force a block update on all leaves to recalculate their distance
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dy = 0; dy <= height; dy++) {
                    for (int dz = -radius; dz <= radius; dz++) {
                        BlockPos pos = BlockPos.containing(centerX + dx, centerY + dy, centerZ + dz);
                        BlockState state = world.getBlockState(pos);

                        // Check if the block is a leaf block
                        if (state.getBlock() == SarosFruitTreesModModBlocks.FRUIT_LEAVE.get() ||
                            state.getBlock() == net.minecraft.world.level.block.Blocks.OAK_LEAVES) {

                            // Trigger a block update to recalculate distance
                            if (world instanceof net.minecraft.world.level.Level) {
                                // In 1.21.5, blockUpdated was replaced with updateNeighborsAt
                                ((net.minecraft.world.level.Level) world).updateNeighborsAt(pos, state.getBlock());
                            }

                            // For our custom leaves, make sure they're not persistent and have correct distance
                            if (state.getBlock() == SarosFruitTreesModModBlocks.FRUIT_LEAVE.get()) {
                                if (state.hasProperty(BlockStateProperties.PERSISTENT) &&
                                    state.getValue(BlockStateProperties.PERSISTENT)) {
                                    world.setBlock(pos, state.setValue(BlockStateProperties.PERSISTENT, false), 3);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Gets a random tree structure name from the available structure
     * @param world The server level
     * @param isGoldenApple Whether this is a golden apple tree
     * @param isOrange Whether this is an orange tree
     * @return The structure name
     */
    public static String getRandomTreeStructureName(ServerLevel world, boolean isGoldenApple, boolean isOrange) {
        // All tree types use baum1.nbt to baum4.nbt (baum5 removed due to bugs)
        String[] treeStructureNames = {"baum1", "baum2", "baum3", "baum4"};
        return treeStructureNames[world.random.nextInt(treeStructureNames.length)];
    }
}

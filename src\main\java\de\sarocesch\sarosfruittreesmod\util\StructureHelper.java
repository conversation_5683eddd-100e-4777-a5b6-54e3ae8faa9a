package de.sarocesch.sarosfruittreesmod.util;

import net.minecraft.util.RandomSource;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.levelgen.structure.templatesystem.BlockIgnoreProcessor;
import net.minecraft.world.level.levelgen.structure.templatesystem.StructurePlaceSettings;

/**
 * Hilfsklasse für die Platzierung von Strukturen
 */
public class StructureHelper {

    /**
     * Erstellt verbesserte Einstellungen für die Platzierung von Strukturen
     * Diese Einstellungen verhindern, dass Strukturen gewaltsam spawnen und Blöcke ersetzen
     *
     * @param random Die Zufallsquelle
     * @return Die verbesserten Einstellungen
     */
    public static StructurePlaceSettings createImprovedSettings(RandomSource random) {
        return new StructurePlaceSettings()
            // Zufällige Spiegelung
            .setMirror(Mirror.values()[random.nextInt(2)])
            // Zufällige Rotation
            .setRotation(Rotation.values()[random.nextInt(3)])
            // Zufallsquelle
            .setRandom(random)
            // Ignoriere Luft und Struktur-Blöcke
            .addProcessor(BlockIgnoreProcessor.STRUCTURE_AND_AIR)
            // Ignoriere Entities
            .setIgnoreEntities(true)
            // In Forge 1.20.4 gibt es keine setIntegrity-Methode mehr
            // Die Integrität ist standardmäßig 1.0f (100%)
            // In 1.21 wurde setKeepLiquids entfernt und shouldApplyWaterlogging nimmt keine Parameter mehr an
            // Standardmäßig ist shouldApplyWaterlogging auf false gesetzt, daher müssen wir es nicht explizit aufrufen
            ;
    }
}
